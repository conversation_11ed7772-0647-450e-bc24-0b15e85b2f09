import { ref } from 'vue';
import { CopyIcon, CheckIcon } from '../../utils/Icons.js';

export default {
    name: 'UserMessage',
    props: {
        id: {
            type: String,
            required: true
        },
        content: {
            type: String,
            required: true
        },
        timestamp: {
            type: String,
            default: ''
        }
    },
    setup() {
        const copied = ref(false);

        // 复制消息内容
        const copyContent = (text) => {
            navigator.clipboard.writeText(text)
                .then(() => {
                    copied.value = true;
                    setTimeout(() => {
                        copied.value = false;
                    }, 2000);
                })
                .catch(err => {
                    console.error('复制失败:', err);
                });
        };

        return {
            copyContent,
            copied,
            CopyIcon,
            CheckIcon
        };
    },
    template: `
        <div :id="'message-' + id" class="flex flex-col items-end mb-6 group min-w-0 message user-message px-4" :data-message-id="id">
            <div class="card relative w-auto max-w-[75%] ml-auto rounded-2xl min-w-0 user-message-bubble">
                <div class="card-body px-3 py-2 min-w-0 user-message-content">
                    <p class="whitespace-pre-wrap break-words">{{ content }}</p>
                </div>
            </div>

            <div class="flex items-center justify-end gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-all duration-300 message-footer user-message-footer">
                <span class="text-xs opacity-70 message-timestamp">{{ timestamp }}</span>
                <button
                    class="btn btn-square btn-xs btn-ghost message-action-button"
                    @click="copyContent(content)"
                    title="复制"
                >
                    <span v-if="!copied" class="message-action-icon" v-html="CopyIcon"></span>
                    <span v-else class="message-action-icon" v-html="CheckIcon"></span>
                </button>
            </div>
        </div>
    `
};
