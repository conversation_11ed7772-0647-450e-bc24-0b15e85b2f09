"""
Dashboard history repository module.

This module provides data access functions for chatbot history in the dashboard.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from mysql.connector import Error

from src.utils.logger import logger
from src.utils.env_utils import get_admin_users
from src.db.connection import execute_db_query, get_db_connection

# Load admin users from environment variable
ADMIN_USERS = set(get_admin_users())


def get_filtered_conversation_count_for_dashboard(filters: Optional[Dict[str, Any]] = None) -> int:
    """
    Get the count of unique conversations matching the specified filters.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.

    Returns:
        int: The count of conversations matching the filters
    """
    if filters is None:
        filters = {}

    base_query = "SELECT COUNT(DISTINCT conversation_id) as count FROM chat_history"
    where_clauses, params = _build_where_clauses_from_filters(filters)

    # Combine query
    query = base_query
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)

    try:
        result = execute_db_query(query, tuple(params), fetch='one')
        count = result.get('count', 0) if result else 0
        logger.info(f"Dashboard query returned {count} unique conversations")
        return count
    except Error as e:
        # Error already logged
        return 0
    except Exception as e:
        logger.error(f"Unexpected error counting dashboard conversations: {e}", exc_info=True)
        return 0


def query_conversations_for_dashboard(filters: Optional[Dict[str, Any]] = None,
                                     limit: int = 20,
                                     offset: int = 0) -> Dict[str, List[Dict[str, Any]]]:
    """
    Query chatbot conversations from MySQL for the dashboard, grouped by conversation_id.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.
        limit (int, optional): Maximum number of conversations to return. Defaults to 20.
        offset (int, optional): Number of conversations to skip. Defaults to 0.

    Returns:
        Dict[str, List[Dict[str, Any]]]: A dictionary of conversations, keyed by conversation_id
    """
    if filters is None:
        filters = {}

    conn = None
    cursor = None
    conversations = {}

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Step 1: Get paginated conversation IDs
        where_clauses, params = _build_where_clauses_from_filters(filters)

        convo_sql = """
            SELECT conversation_id, MAX(timestamp) as last_message_time,
                   MAX(is_bad_case) as is_bad_case,
                   MIN(username) as username,
                   MIN(email) as email
            FROM chat_history
        """

        if where_clauses:
            convo_sql += " WHERE " + " AND ".join(where_clauses)

        convo_sql += """
            GROUP BY conversation_id
            ORDER BY last_message_time DESC
            LIMIT %s OFFSET %s
        """

        query_params = params + [limit, offset]
        logger.debug(f"Executing conversation ID query: {convo_sql} | Params count: {len(query_params)}")
        cursor.execute(convo_sql, tuple(query_params))
        paginated_convos = cursor.fetchall()

        if not paginated_convos:
            logger.info(f"No conversations found with the specified filters (limit={limit}, offset={offset})")
            return {}

        # Extract conversation IDs and metadata
        paginated_convo_ids = []
        convo_metadata = {}

        for row in paginated_convos:
            convo_id = row['conversation_id']
            paginated_convo_ids.append(convo_id)

            # Store metadata for each conversation
            convo_metadata[convo_id] = {
                'is_bad_case': bool(row['is_bad_case']),
                'username': row['username'],
                'email': row['email'],
                'last_message_time': row['last_message_time']
            }

        # Step 2: Get all messages for these conversations
        placeholders = ','.join(['%s'] * len(paginated_convo_ids))
        messages_sql = f"""
            SELECT id, conversation_id, role, content, logs, timestamp, is_bad_case,
                   username, email, output_as_input
            FROM chat_history
            WHERE conversation_id IN ({placeholders})
            ORDER BY conversation_id, timestamp ASC
        """

        cursor.execute(messages_sql, tuple(paginated_convo_ids))
        rows = cursor.fetchall()

        # Step 3: Group messages by conversation ID
        for row in rows:
            convo_id = row['conversation_id']
            if convo_id not in conversations:
                conversations[convo_id] = []

            # Format timestamp
            ts_ms = row.get('timestamp')
            formatted_time = None
            if ts_ms and isinstance(ts_ms, int):
                try:
                    dt_object = datetime.fromtimestamp(ts_ms / 1000)
                    formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
                except (TypeError, ValueError, OSError):
                    formatted_time = f"Invalid timestamp: {ts_ms}"
            else:
                formatted_time = "No timestamp"

            # Truncate long content preview
            content = row.get('content', '')
            if not isinstance(content, str):
                content = str(content)
            content_preview = (content[:100] + '...') if len(content) > 100 else content

            # Add message to conversation
            message = {
                'id': row['id'],
                'role': row['role'] or 'unknown',
                'content': content,
                'content_preview': content_preview,
                'logs': row['logs'],
                'timestamp': ts_ms,
                'formatted_time': formatted_time,
                'is_bad_case': bool(row['is_bad_case']),
                'username': row['username'],
                'email': row['email'],
                'output_as_input': row['output_as_input']
            }
            conversations[convo_id].append(message)

        # Step 4: Add metadata to each conversation
        result = {}
        for convo_id in paginated_convo_ids:
            if convo_id in conversations:
                # Get first user message as title
                title = "Untitled Conversation"
                for msg in conversations[convo_id]:
                    if msg['role'] == 'user':
                        title = msg['content_preview']
                        break

                # Add metadata
                metadata = convo_metadata.get(convo_id, {})
                result[convo_id] = {
                    'conversation_id': convo_id,
                    'title': title,
                    'is_bad_case': metadata.get('is_bad_case', False),
                    'username': metadata.get('username', ''),
                    'email': metadata.get('email', ''),
                    'last_message_time': metadata.get('last_message_time'),
                    'messages': conversations[convo_id]
                }

        logger.info(f"Dashboard query returned {len(result)} conversations")
        return result

    except Error as e:
        # Error already logged
        return {}
    except Exception as e:
        logger.error(f"Unexpected error querying dashboard conversations: {e}", exc_info=True)
        return {}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()


def _build_where_clauses_from_filters(filters: Dict[str, Any]) -> Tuple[List[str], List[Any]]:
    """
    Build WHERE clauses and parameters from filters.

    Args:
        filters (Dict[str, Any]): Filters to apply to the query.

    Returns:
        Tuple[List[str], List[Any]]: A tuple of (where_clauses, params)
    """
    where_clauses = []
    params = []

    # Username filter
    if filters.get('username'):
        where_clauses.append("username = %s")
        params.append(str(filters['username']))

    # Email filter
    if filters.get('email'):
        where_clauses.append("email = %s")
        params.append(str(filters['email']))

    # Role filter
    if filters.get('role'):
        where_clauses.append("role = %s")
        params.append(str(filters['role']))

    # Start date filter
    if filters.get('start_date'):
        try:
            # Assume start_date is in 'YYYY-MM-DD' format, need millisecond timestamp for 00:00:00 of that day
            start_dt = datetime.strptime(str(filters['start_date']), '%Y-%m-%d')
            start_timestamp = int(start_dt.timestamp() * 1000)
            where_clauses.append("timestamp >= %s")
            params.append(start_timestamp)
        except (ValueError, TypeError):
            logger.warning(f"Invalid start_date format or type: {filters['start_date']}")

    # End date filter
    if filters.get('end_date'):
        try:
            # Assume end_date is in 'YYYY-MM-DD' format, need millisecond timestamp for 23:59:59.999 of that day
            end_dt = datetime.strptime(str(filters['end_date']) + " 23:59:59", '%Y-%m-%d %H:%M:%S')
            end_timestamp = int(end_dt.timestamp() * 1000) + 999  # Include milliseconds
            where_clauses.append("timestamp <= %s")
            params.append(end_timestamp)
        except (ValueError, TypeError):
            logger.warning(f"Invalid end_date format or type: {filters['end_date']}")

    # Content search filter
    if filters.get('content_search'):
        where_clauses.append("content LIKE %s")
        params.append(f"%{str(filters['content_search'])}%")

    # Conversation ID filter
    if filters.get('conversation_id'):
        where_clauses.append("conversation_id = %s")
        params.append(str(filters['conversation_id']))

    # Bad case filter
    if 'bad_case_filter' in filters and filters['bad_case_filter'] is not None:
        if filters['bad_case_filter'] == 'only_bad':
            where_clauses.append("is_bad_case = 1")
        elif filters['bad_case_filter'] == 'only_good':
            where_clauses.append("is_bad_case = 0")

    # Filter out admin users if requested
    if filters.get('filter_admin') and ADMIN_USERS:
        admin_placeholders = ','.join(['%s'] * len(ADMIN_USERS))
        where_clauses.append(f"username NOT IN ({admin_placeholders})")
        params.extend(list(ADMIN_USERS))

    return where_clauses, params
