import { ref, computed, onMounted } from 'vue';
import { SendIcon, SquareIcon } from '../../utils/Icons.js';
import { sendQuery } from '../services/queryService.js';

export default {
    name: 'ChatInput',
    props: {
        conversationId: {
            type: String,
            default: null
        },
        isDevLogVisible: {
            type: Boolean,
            default: false
        }
    },
    emits: ['message-sent', 'message-start', 'message-stream', 'message-complete', 'message-error', 'message-interrupting', 'toggle-dev-log'],
    setup(props, { emit }) {
        const message = ref('');
        const isLoading = ref(false);
        const isStreaming = ref(false);
        const textareaRef = ref(null);
        const queryController = ref(null);

        // 根据内容自动调整高度
        const adjustTextareaHeight = () => {
            if (!textareaRef.value) return;

            // 重置高度以获取正确的scrollHeight
            textareaRef.value.style.height = 'auto';

            // 设置新高度，但限制最大高度
            const newHeight = Math.min(Math.max(textareaRef.value.scrollHeight, 24), 200);
            textareaRef.value.style.height = `${newHeight}px`;
        };

        // 计算输入框的行数
        const textareaRows = computed(() => {
            const lineCount = (message.value.match(/\n/g) || []).length + 1;
            return Math.min(lineCount, 8); // 最多显示8行
        });

        // 中断当前查询
        const interruptQuery = () => {
            if (queryController.value) {
                // 显示中断状态
                emit('message-interrupting');

                // 中断请求
                queryController.value.abort();

                // 重置状态，但不清除消息
                // 注意：不再发送message-error，因为这会导致消息被清除
                // 中断处理现在在queryService中完成，并通过onMessageComplete回调通知
                queryController.value = null;
                isStreaming.value = false;
                isLoading.value = false;
            }
        };

        // 发送消息
        const sendMessage = async () => {
            // 如果正在流式传输，则中断当前查询
            if (isStreaming.value) {
                interruptQuery();
                return;
            }

            // 检查消息是否为空或正在加载
            if (!message.value.trim() || isLoading.value) return;

            try {
                isLoading.value = true;
                const userMessage = message.value.trim();

                // 清空输入框并重置高度
                message.value = '';
                if (textareaRef.value) {
                    textareaRef.value.style.height = '24px';
                }

                // 通知父组件用户消息已发送
                emit('message-sent', {
                    content: userMessage,
                    timestamp: Date.now()
                });

                // 通知父组件开始AI回复
                emit('message-start');

                // 开始流式查询
                isStreaming.value = true;
                queryController.value = sendQuery({
                    query: userMessage,
                    conversationId: props.conversationId,
                    onMessageStart: (state) => {
                        console.log('开始接收AI回复', state);
                    },
                    onMessageUpdate: (chunk, state) => {
                        // 通知父组件消息流更新
                        const streamData = {
                            chunk,
                            fullMessage: state.rawContent,
                            renderedContent: state.renderedContent,
                            state
                        };
                        emit('message-stream', streamData);
                    },
                    onMessageComplete: (state) => {
                        isStreaming.value = false;
                        isLoading.value = false;
                        queryController.value = null;

                        // 通知父组件消息完成
                        const completeData = {
                            content: state.rawContent,
                            renderedContent: state.renderedContent,
                            conversationId: state.conversationId || props.conversationId,
                            logs: state.logsHTML,
                            timestamp: Date.now(),
                            isInterrupted: state.isInterrupted || false // 添加中断状态标记
                        };
                        emit('message-complete', completeData);
                    },
                    onError: (errorMessage, state) => {
                        console.error('查询错误:', errorMessage, state);
                        isStreaming.value = false;
                        isLoading.value = false;
                        queryController.value = null;

                        // 通知父组件发生错误
                        emit('message-error', {
                            message: errorMessage,
                            state
                        });
                    }
                });

            } catch (error) {
                console.error('发送消息失败:', error);
                isStreaming.value = false;
                isLoading.value = false;
                queryController.value = null;
                emit('message-error', error.message || '发送消息失败');
            }
        };

        // 处理按键事件
        const handleKeydown = (event) => {
            // 按下Enter键发送消息，按下Shift+Enter换行
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        };

        // 处理输入事件，调整高度
        const handleInput = () => {
            adjustTextareaHeight();
        };

        // 组件挂载后初始化
        onMounted(() => {
            if (textareaRef.value) {
                textareaRef.value.style.height = '24px';
            }
        });

        return {
            message,
            isLoading,
            isStreaming,
            textareaRef,
            textareaRows,
            sendMessage,
            interruptQuery,
            handleKeydown,
            handleInput,
            SendIcon,
            SquareIcon
        };
    },
    template: `
        <div class="w-full px-4 md:px-10 min-w-0 chat-input-container">
            <div class="flex flex-col w-full border min-w-0 overflow-hidden chat-input-box">
                <!-- 输入区域 -->
                <div class="w-full px-3 pt-3 pb-1 chat-input-area">
                    <textarea
                        ref="textareaRef"
                        v-model="message"
                        class="w-full resize-none focus:outline-none border-0 bg-transparent min-w-0 p-0 text-[16px] leading-6 min-h-[24px] max-h-[200px] chat-input-textarea scrollbar-auto"
                        placeholder="输入您的问题..."
                        :rows="textareaRows"
                        @keydown="handleKeydown"
                        @input="handleInput"
                        style="height: 24px; background-color: transparent;"
                    ></textarea>
                </div>

                <!-- 功能按钮区域 -->
                <div class="flex items-center justify-between py-1.5 px-3 chat-input-footer">
                    <span class="text-xs font-light tracking-wide chat-input-footer-text">
                        Developed by XM, Powered by DeepSeek
                    </span>
                    <div class="flex items-center">
                        <!-- 发送按钮 -->
                        <button
                            :class="[
                                'p-2.5 rounded-full transition-all duration-200 disabled:cursor-not-allowed chat-input-send-button',
                                isStreaming || (message.trim() && !isLoading) ? 'chat-input-send-button-active' : 'chat-input-send-button-inactive'
                            ]"
                            :disabled="(!message.trim() && !isStreaming) || (isLoading && !isStreaming)"
                            @click="sendMessage"
                            :title="isStreaming ? '中断查询' : '发送'"
                        >
                            <span v-if="isLoading && !isStreaming" class="loading loading-spinner loading-sm"></span>
                            <span v-else-if="isStreaming" class="w-5 h-5 chat-input-interrupt-icon" v-html="SquareIcon"></span>
                            <span v-else class="w-5 h-5 chat-input-send-icon" v-html="SendIcon"></span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="text-xs text-base-content/50 mt-2 text-center chat-input-hint">
                ChatBI 的回答未必准确无误，请仔细核查数据
            </div>
        </div>
    `
};
