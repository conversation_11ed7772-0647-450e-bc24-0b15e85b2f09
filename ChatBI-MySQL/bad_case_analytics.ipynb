import requests
import os

from dotenv import load_dotenv

load_dotenv()

WORKING_DIR = "resources/ddl"
OPENAI_API_KEY = os.getenv("XM_OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE")
OPENAI_MODEL = 'deepseek/deepseek-r1-0528:free' # os.getenv("OPENAI_MODEL")

print("my ip:" + requests.get("https://ifconfig.io/ip").text)

pip install mysql-connector-python

CHATBI_DB_HOST = os.getenv("CHATBI_MYSQL_HOST", "mysql-xm.summerfarm.net")
CHATBI_DB_PORT = int(os.getenv("CHATBI_MYSQL_PORT", 3308))
CHATBI_DB_USER = os.getenv("CHATBI_MYSQL_USER", "qa")
CHATBI_DB_PASSWORD = os.getenv("CHATBI_MYSQL_PASSWORD", "xianmu619")
CHATBI_DB_NAME = 'chatbi'
CHATBI_POOL_NAME = "chatbi_pool"
CHATBI_POOL_SIZE = int(os.getenv("CHATBI_MYSQL_POOL_SIZE", 5)) # 可配置的池大小
CHATBI_CONNECT_TIMEOUT = int(os.getenv("CHATBI_CONNECT_TIMEOUT", 10)) # 连接超时（秒）

# 建立数据库连
import mysql.connector
from mysql.connector import pooling

dbconfig = {
    "host": CHATBI_DB_HOST,
    "port": CHATBI_DB_PORT,
    "user": CHATBI_DB_USER,
    "password": CHATBI_DB_PASSWORD,
    "database": CHATBI_DB_NAME,
    "connection_timeout": CHATBI_CONNECT_TIMEOUT
}

pool = mysql.connector.pooling.MySQLConnectionPool(
    pool_name=CHATBI_POOL_NAME,
    pool_size=CHATBI_POOL_SIZE,
    **dbconfig
)

conn=pool.get_connection()
cur = conn.cursor()
cur.execute("""select max(id) as max_id,conversation_id,username,group_concat(content) as user_content,
            date_format(max(created_at),'%Y-%m-%d') as last_created_at,
            concat('https://chat-bi.summerfarm.net/dashboard?chat=',conversation_id) as dashboard_link
            from chat_history 
            where is_bad_case = 1 and role = 'user' 
            group by conversation_id,username
            order by max_id desc limit 20;""")
rows = cur.fetchall()

# 将结果格式化为markdown表格
print("| 会话ID | 用户名 | 用户内容 | 最后创建时间 | 仪表盘链接 |")
print("|--------|--------|----------|--------------|------------|")

for row in rows:
    # 转义markdown特殊字符
    content = str(row[3]).replace('|', '\\|').replace('\n', ' ')
    print(f"| {row[1]} | {row[2]} | {content} | {row[4]} | {row[5]} |")

from openai import OpenAI
import os

client = OpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)

OPENAI_MODEL = os.getenv("OPENAI_MODEL")

SYSTEM_PROMPT = """请你根据以下表结构定义语句，以及样例数据，重新补充完整DDL语句的注释部分。

请注意：
1. 注释部分需要用中文描述。
2. 注释部分需要详细描述表的各个字段的含义。
3. 注释部分用200字来作为该表的注释描述表的主要功能。
4. 结合样例数据，尽可能为每个字段提供数据举例。
5. 为了避免繁杂的数据，请你把DDL语句精简，把那些和业务无关的字段和索引部分去掉。
这样AI才好用来根据DDL语句编写SQL，用以回答问题。"""


def refine_ddl(ddl_doc: str) -> str:
    response = client.chat.completions.create(
        model=OPENAI_MODEL,
        messages=[
            {
                "role": "prompt",
                "content": SYSTEM_PROMPT,
            },
            {
                "role": "user",
                "content": f"请你直接返回补充完整后的DDL语句，不要返回其他内容:\n\n{ddl_doc}",
            },
        ],
        stream=True,
    )

    # 使用生成器表达式迭代处理流式响应
    # response.choices[0].delta.content 总是 None, 因此需要迭代
    collected_chunks = []
    for chunk in response:
        if chunk.choices[0].delta.content is not None:
            chunk_text = chunk.choices[0].delta.content
            print(chunk_text, end="")
            collected_chunks.append(chunk_text)
    return "".join(collected_chunks)


merchant_tables = [
    "orders",
    "order_item",
    "merchant",
    "inventory",
    # "products",
    # "category",
    # "crm_bd_org",
    # "follow_up_record",
    # "follow_up_relation",
    "warehouse_storage_center",
    # "warehouse_logistics_center",
    "area",
    "delivery_plan",
    # "admin",
]

total_docs = ""

for table_name in merchant_tables:
    with open(f"{WORKING_DIR}/{table_name}_ddl.md", "r") as f:
        ddl_doc = f.read()
    refined_ddl_doc = refine_ddl(ddl_doc)
    with open(f"{WORKING_DIR}/{table_name}_ddl_refined.md", "w") as f:
        f.write(refined_ddl_doc)
        total_docs += refined_ddl_doc
        total_docs += "\n\n"
        
# write total_docs into file:
with open(f"{WORKING_DIR}/total_docs.md", "w") as f:
    f.write(total_docs)

# read from file f"{WORKING_DIR}/total_docs.md"
total_docs = open(f"{WORKING_DIR}/total_docs.md", "r").read()


from typing_extensions import Any, Dict
from openai import AsyncOpenAI

# 导入 agents 库的相关组件
from agents import (
    Agent,
    Runner,
    function_tool,
    set_tracing_disabled,
    OpenAIChatCompletionsModel,
    ModelProvider,
    Model,
    RunConfig,
)

# 导入自定义的 MySQL 客户端函数
from services.db.mysql_client import execute_sql_query
from openai.types.responses import ResponseTextDeltaEvent

# 假设 OPENAI_API_KEY, OPENAI_API_BASE, OPENAI_MODEL, total_docs 在之前的单元格已定义

# 设置 agents 库使用的默认 OpenAI 客户端
# 确保 OPENAI_API_KEY 和 OPENAI_API_BASE 已正确设置
client = AsyncOpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)
set_tracing_disabled(disabled=True)


class CustomModelProvider(ModelProvider):
    def get_model(self, model_name: str = None) -> Model:
        return OpenAIChatCompletionsModel(
            model=model_name or OPENAI_MODEL, openai_client=client
        )


CUSTOM_MODEL_PROVIDER = CustomModelProvider()


# 定义一个函数工具，用于执行 MySQL 查询
@function_tool
async def fetch_mysql_sql_result(
    sql: str, description: str
) -> tuple[Dict[str, Any], str]:
    """使用SQL来查询MySQL数据库，返回查询结果(字典格式)和对查询的描述。

    Args:
        sql: 要执行的 SQL 查询语句。
        description: 对 SQL 查询的简短描述。
    """
    # 调用 mysql_client 中的函数执行查询
    # 假设 execute_sql_query 返回一个适合 agent 使用的字典格式结果
    return execute_sql_query(sql), description

# 主异步函数
async def main():
    # 创建 Agent 实例
    # 确保提供了模型名称和所需的工具
    agent = Agent(
        name="MySQL Expert Assistant",  # 更具描述性的 Agent 名称
        instructions=f"你是一个MySQL数据专家，专门帮助用户查询MySQL数据库。以下是数据库的DDL语句：\n\n{total_docs}\n\n请根据用户的问题生成并执行SQL查询。请注意不要使用with子句，因为该子句已经被服务端禁止。",
        model=OPENAI_MODEL,  # 明确指定要使用的模型
        tools=[
            fetch_mysql_sql_result,  # 将 SQL 查询工具提供给 Agent
        ],
    )

    # 定义用户输入的问题
    user_input = "查询注册城市为杭州市的用户，3月下单额比对2月下单额下降最大的20个门店是哪些？列出门店名称和下单金额、最后下单日、下单商品列表（聚合），按照2月下单额降序排序"

    print(f"User Query: {user_input}\n")
    print("Agent Response:\n")

    # 使用 Runner 流式运行 Agent
    result = Runner.run_streamed(
        agent,
        input=user_input,
        run_config=RunConfig(model_provider=CUSTOM_MODEL_PROVIDER),
    )

    # 异步迭代处理流式事件
    async for event in result.stream_events():
        # 检查事件类型是否为原始响应事件，并且数据是文本增量
        # 注意：'raw_response_event' 是 agents 库定义的事件类型，需要确认是否准确
        # 如果 agents 库有更具体的事件类型如 'text_delta_event'，应使用那个
        if event.type == "raw_response_event" and isinstance(
            event.data, ResponseTextDeltaEvent
        ):
            # 打印收到的文本增量，不换行，并立即刷新输出
            # 检查 delta 是否为 None，虽然理论上 isinstance 检查后不应为 None，但增加健壮性
            if event.data.delta:
                print(event.data.delta, end="", flush=True)

    print("\n--- End of Response ---")  # 标记响应结束


await main()

