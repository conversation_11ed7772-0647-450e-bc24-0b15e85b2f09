<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel=icon type=image/x-icon href=https://azure.summerfarm.net/favicon.ico rel=prefetch>
    <title>ChatBI - 智能数据分析助手</title>

    <!-- Tailwind CSS and DaisyUI via CDN (Latest version) -->
    <script src="{{ url_for('static', filename='js/lib/tailwindcss-3.4.16.js', t=cache_control_timestamp) }}"></script>
    <link href="https://cdn.bootcdn.net/ajax/libs/daisyui/4.7.3/full.css" rel="stylesheet">
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'gradient': 'gradient 15s ease infinite',
                    },
                    keyframes: {
                        gradient: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                        },
                    },
                },
            },
            daisyui: {
                themes: ["light", "dark"],
                darkTheme: "dark",
            },
        }
    </script>

    <style>
        /* 针对 dark 主题进行覆盖 */
        [data-theme="dark"] {
            /* 覆盖基础背景色变量 --b1 */
            --b1: #24292f;
            /* 你想设置的新的深色背景 */
        }
    </style>

    <!-- Import Maps for Vue.js -->
    <script type="importmap">
        {
            "imports": {
                "vue": "https://unpkg.com/vue@3.5.13/dist/vue.esm-browser.js"
            }
        }
    </script>

    <!-- We'll implement our own state management solution using Vue's reactivity -->


    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css', t=cache_control_timestamp) }}" />

    <!-- 内联样式已移动到组件CSS文件中 -->
</head>

<body class="min-h-screen bg-base-100">
    <div class="fixed inset-0 bg-gradient-animated -z-10"></div>

    <div id="app" class="relative min-h-screen">
        <!-- 应用将在这里挂载 -->
    </div>


    <!-- Marked.js - Markdown 解析库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/marked/11.2.0/marked.min.js"></script>
    <!-- Marked Highlight 扩展 - 用于代码高亮集成 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/marked-highlight/2.2.1/index.umd.min.js"></script>

    <!-- Highlight.js - 语法高亮库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/styles/atom-one-light.min.css"
        id="light-hljs-theme" media="(prefers-color-scheme: light)">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css"
        id="dark-hljs-theme" media="(prefers-color-scheme: dark)">
    <script src="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <!-- 常用语言包 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/languages/sql.min.js"></script>

    <!-- 初始化 Markdown 和代码高亮 -->
    <script type="module">
        import {
            initializeMarked
        } from "{{ url_for('static', filename='js/utils/MarkdownRenderer.js', t=cache_control_timestamp) }}";
        import {
            initializeHighlighter,
            updateCodeHighlightTheme
        } from "{{ url_for('static', filename='js/utils/CodeHighlighter.js', t=cache_control_timestamp) }}";

        document.addEventListener('DOMContentLoaded', function () {
            // 初始化marked.js
            initializeMarked();

            // 初始化highlight.js
            initializeHighlighter();

            // 根据当前主题切换代码高亮主题
            updateCodeHighlightTheme(document.documentElement.getAttribute('data-theme') || 'light');

            // 监听主题变化
            const themeObserver = new MutationObserver(function (mutations) {
                mutations.forEach(function (mutation) {
                    if (mutation.attributeName === 'data-theme') {
                        const theme = document.documentElement.getAttribute('data-theme') || 'light';
                        updateCodeHighlightTheme(theme);
                    }
                });
            });

            // 观察html元素的data-theme属性变化
            themeObserver.observe(document.documentElement, {
                attributes: true,
                attributeFilter: ['data-theme']
            });
        });
    </script>

    <!-- 主应用脚本 -->
    <script type="module" src="{{ url_for('static', filename='js/app.js', t=cache_control_timestamp) }}"></script>

    <!-- 用户信息传递给前端 -->
    <script>
        window.userInfo = {
            name: "{{ user_name }}",
            avatar: "{{ user_avatar_thumb }}",
            email: "{{ user_email }}",
            jobTitle: "{{ job_title }}",
            isAdmin: {{ 'true' if is_admin else 'false' }},
        appRoot: "{{ app_root }}"
    };

        {% if is_shared %}
        window.sharedConversation = {
            isShared: true,
            isOwner: {{ 'true' if is_owner else 'false' }},
        shareId: "{{ share_id }}",
            ownerName: "{{ share_owner_name }}",
                messages: {{ shared_conversation | tojson }}
        };
        {% else %}
        window.sharedConversation = {
            isShared: false
        };
        {% endif %}
    </script>
</body>


</html>