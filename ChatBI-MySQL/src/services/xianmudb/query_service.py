"""
XianmuDB query execution service module.

This module provides business logic for executing queries against the XianmuDB business database.
"""
from db.query import execute_sql_query
from src.utils.logger import logger
from src.db.database_enum import Database
from src.models.query_result import SQLQueryResult

def execute_business_query(sql_query: str) -> SQLQueryResult:
    """
    Execute a SQL query against the XianmuDB business database.

    This function is specifically for querying the business database (xianmudb).
    It adds additional logging and error handling specific to business queries.

    Args:
        sql_query (str): The SQL query to execute

    Returns:
        SQLQueryResult: The result of the query
    """
    logger.debug(f"Executing business query against XianmuDB: {sql_query}")

    # Validate the query (basic check to prevent destructive operations)
    sql_lower = sql_query.lower().strip()
    if not sql_lower.startswith('select'):
        error_msg = "Only SELECT queries are allowed for business database"
        logger.warning(f"{error_msg}: {sql_query}")
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            message=error_msg,
            success=False
        )

    try:
        # Execute the query against the business database
        result = execute_sql_query(sql_query, database=Database.BUSINESS)

        # Log the result
        if result.success:
            row_count = len(result.data) if result.data else 0
            logger.info(f"Business query executed successfully, returned {row_count} rows")
        else:
            logger.warning(f"Business query failed: {result.error}")

        return result

    except Exception as e:
        error_msg = f"Error executing business query: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return SQLQueryResult(
            columns=None,
            data=None,
            error=error_msg,
            message=error_msg,
            success=False
        )
