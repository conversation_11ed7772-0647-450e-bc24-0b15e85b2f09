"""
Chat bad case repository module.

This module provides data access functions for managing bad case conversations.
"""

from typing import Optional
from mysql.connector import <PERSON>rror

from src.utils.logger import logger
from src.db.connection import execute_db_query

def mark_conversation_as_bad_case(conversation_id: str, is_bad_case: bool = True) -> bool:
    """
    Mark or unmark all messages in a specified conversation ID as bad cases in MySQL.

    Args:
        conversation_id (str): The ID of the conversation to mark
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    if not conversation_id:
         logger.warning("Conversation ID is required to mark a bad case")
         return False

    # Convert boolean to integer (0 or 1)
    is_bad_case_int = 1 if is_bad_case else 0

    sql = """
        UPDATE chat_history
        SET is_bad_case = %s
        WHERE conversation_id = %s
    """
    values = (is_bad_case_int, conversation_id)

    try:
        affected_rows = execute_db_query(sql, values, fetch='count', commit=True)
        action = "marked" if is_bad_case else "unmarked"
        logger.info(f"Successfully {action} conversation {conversation_id} as {'bad case' if is_bad_case else 'not bad case'}, {affected_rows} messages affected")
        return True
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error marking conversation as bad case: {e}", exc_info=True)
        return False

def get_conversation_bad_case_status(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a bad case.

    Args:
        conversation_id (str): The ID of the conversation to check
        username (str, optional): The username to filter by. Defaults to None.
        email (str, optional): The email to filter by. Defaults to None.

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    if not conversation_id:
        logger.warning("Conversation ID is required to check bad case status")
        return False

    # Base query
    sql = "SELECT is_bad_case FROM chat_history WHERE conversation_id = %s"
    params = [conversation_id]

    # Add username and email filters if provided
    if username:
        sql += " AND username = %s"
        params.append(username)
    if email:
        sql += " AND email = %s"
        params.append(email)

    # Limit to one row since we just need to check if any message is marked
    sql += " LIMIT 1"

    try:
        result = execute_db_query(sql, tuple(params), fetch='one')
        # If result exists and is_bad_case is 1, return True
        return bool(result and result.get('is_bad_case', 0))
    except Error as e:
        # Error already logged
        return False
    except Exception as e:
        logger.error(f"Unexpected error checking bad case status: {e}", exc_info=True)
        return False
