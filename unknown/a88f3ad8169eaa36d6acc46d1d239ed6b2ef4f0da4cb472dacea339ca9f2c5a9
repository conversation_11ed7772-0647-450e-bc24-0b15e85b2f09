/**
 * Message Component Styles
 *
 * These styles enhance the existing Tailwind classes without replacing them.
 * They provide theme-based styling using CSS variables following Apple/OpenAI aesthetics.
 * Uses font variables from font.css for consistent typography.
 */

/* Common message styles - minimal to avoid conflicts */
.message {
  /* No layouts properties to avoid conflicts with Tailwind */
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

.message-group {
  /* Minimal styling to avoid conflicts */
  transition: margin var(--duration-normal);
}

/* Message timestamp and actions - enhance without replacing */
.message-footer {
  /* Transition is already handled by Tailwind classes */
  transition: opacity var(--duration-text);
}

/* 在移动设备上始终显示消息工具栏 */
@media (max-width: 768px) {
  .message-footer {
    opacity: 1 !important; /* 覆盖 Tailwind 的 opacity-0 类 */
    padding-top: 0.25rem; /* 增加一点上边距 */
    padding-bottom: 0.25rem; /* 增加一点下边距 */
  }

  /* 移动端上稍微调整工具栏的样式，使其更加美观 */
  .message-action-button {
    opacity: 0.8; /* 稍微透明一点 */
  }

  .message-timestamp {
    opacity: 0.8 !important; /* 确保时间戳在移动端上可见但不突兀 */
  }
}

.message-timestamp {
  color: var(--color-text-secondary);
  font-size: var(--font-size-xs);
  font-family: var(--font-family-sans);
  transition: color var(--duration-normal), opacity var(--duration-text);
}

.message-action-button {
  /* Button styling is already handled by Tailwind/DaisyUI */
  transition: background-color var(--duration-fast), transform var(--duration-fast);
}

.message-action-button:hover {
  transform: translateY(-1px);
}

.message-action-button:active {
  transform: translateY(0);
}

.message-action-icon {
  color: var(--color-text-secondary);
  transition: color var(--duration-normal);
}

.message-action-icon:hover {
  color: var(--color-text-primary);
}

/* User message specific styles */
.user-message {
  /* Alignment is already handled by Tailwind */
  transition: margin var(--duration-normal);
}

.user-message-bubble {
  background-color: var(--color-bg-bubble);
  border-color: var(--color-border-secondary);
  box-shadow: 0 1px 2px var(--color-shadow);
  transition: background-color var(--duration-normal),
              border-color var(--duration-normal);
}

.user-message-content {
  color: var(--color-text-primary);
  font-family: var(--font-family-sans);
  font-size: 0.9375rem; /* 15px - 比原来的16px小一点 */
  font-weight: 450; /* 介于normal(400)和medium(500)之间的值 */
  line-height: var(--line-height-relaxed);
  transition: color var(--duration-normal);
}

/* 确保暗色模式下用户消息和AI消息文本颜色一致 */
[data-theme="dark"] .user-message-content {
  color: var(--color-text-primary); /* 使用与AI消息相同的颜色变量 */
}

.user-message-footer {
  /* Layout is already handled by Tailwind */
  transition: opacity var(--duration-text);
}

/* AI message specific styles */
.ai-message {
  /* Alignment is already handled by Tailwind */
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

.ai-message-content {
  /* Layout is already handled by Tailwind */
  font-family: var(--font-family-sans);
  font-size: 0.9375rem; /* 15px - 比原来的16px小一点 */
  font-weight: 450; /* 介于normal(400)和medium(500)之间的值 */
  line-height: var(--line-height-relaxed);
  transition: color var(--duration-normal), border-left var(--duration-normal);
  position: relative; /* 为流式传输指示器提供定位上下文 */
}

/* 流式传输中的AI消息 */
.ai-message-streaming .ai-message-content {
  border-left: 3px solid var(--color-accent-blue);
}

/* 错误状态的AI消息 */
.ai-message-error .ai-message-content {
  border-left: 3px solid var(--color-accent-red, #ff3b30);
  background-color: rgba(255, 59, 48, 0.05);
}

/* 被中断的AI消息 */
.ai-message-interrupted .ai-message-content {
  border-left: 3px solid var(--color-accent-orange, #ff9500);
  background-color: rgba(255, 149, 0, 0.05);
}

/* 流式传输指示器 */
.streaming-indicator {
  display: inline-block;
  margin-left: 0.5rem;
  vertical-align: middle;
}

.ai-message-footer {
  /* Layout is already handled by Tailwind */
  transition: opacity var(--duration-text);
}

/* 不良案例标记按钮样式 */
.bad-case-marked {
  opacity: 1 !important;
}

.bad-case-icon {
  color: #e05252 !important; /* 更柔和的红色 */
  transition: color 0.2s ease;
}

[data-theme="dark"] .bad-case-icon {
  color: #ff6b6b !important; /* 暗色模式下更明亮的红色 */
}

/* Markdown content in AI messages */
.markdown-content {
  color: var(--color-text-primary);
  transition: color var(--duration-normal);
}

/* Code blocks in markdown content */
.markdown-content pre {
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-secondary);
  border-radius: 6px;
  transition: background-color var(--duration-normal), border-color var(--duration-normal);
}

/* Tables in markdown content */
.markdown-content table {
  border-color: var(--color-border-secondary);
  transition: border-color var(--duration-normal);
}

.markdown-content th,
.markdown-content td {
  border-color: var(--color-border-secondary);
  transition: border-color var(--duration-normal), background-color var(--duration-normal);
}

.markdown-content th {
  background-color: var(--color-bg-secondary);
}
