"""
Model provider configuration for agent.
"""
import os
from dotenv import load_dotenv
from openai import AsyncOpenAI
from agents import ModelProvider, Model, OpenAIChatCompletionsModel, set_tracing_disabled

# --- Configuration ---
load_dotenv()
# 基础模型配置
OPENAI_API_KEY = os.getenv("XM_OPENAI_API_KEY")
OPENAI_API_BASE = os.getenv("OPENAI_API_BASE")
OPENAI_MODEL = os.getenv("OPENAI_MODEL")

# 强模型配置
STRONG_OPENAI_API_KEY = os.getenv("STRONG_XM_OPENAI_API_KEY", OPENAI_API_KEY)
STRONG_OPENAI_API_BASE = os.getenv("STRONG_OPENAI_API_BASE", OPENAI_API_BASE)
STRONG_OPENAI_MODEL = os.getenv("STRONG_OPENAI_MODEL", OPENAI_MODEL)
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

# Initialize OpenAI client
default_client = AsyncOpenAI(base_url=OPENAI_API_BASE, api_key=OPENAI_API_KEY)
strong_client = AsyncOpenAI(base_url=STRONG_OPENAI_API_BASE, api_key=STRONG_OPENAI_API_KEY)
set_tracing_disabled(disabled=True)


class CustomModelProvider(ModelProvider):
    """Custom model provider for OpenAI models."""

    def __init__(self, model_name: str = None, client: AsyncOpenAI = default_client):
        super().__init__()
        self.model_name = model_name
        self.client = client
    
    def get_model(self, model_name: str = None) -> Model:
        """
        Get the model to use for the agent.
        OPENAI_MODEL
        Args:
            model_name: Optional model name to use, defaults to OPENAI_MODEL from env
            
        Returns:
            Model: The model to use
        """
        return OpenAIChatCompletionsModel(
            model=model_name or self.model_name or OPENAI_MODEL, openai_client=self.client
        )


# Singleton instance for use throughout the application
CUSTOM_MODEL_PROVIDER = CustomModelProvider(client=default_client)
STRONG_MODEL_PROVIDER = CustomModelProvider(client=strong_client, model_name=STRONG_OPENAI_MODEL)
