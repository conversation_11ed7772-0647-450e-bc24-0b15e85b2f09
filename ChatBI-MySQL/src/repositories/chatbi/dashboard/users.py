"""
Dashboard users repository module.

This module provides data access functions for user-related dashboard operations.
"""

import json
import re
import yaml
from typing import List, Dict, Any, Optional
from mysql.connector import Error

from src.utils.logger import logger
from src.utils.env_utils import get_admin_users
from src.utils.resource_manager import list_resources, load_resource
from src.db.connection import execute_db_query

def get_unique_users() -> List[Dict[str, str]]:
    """
    Get all unique users (grouped by email and username) from MySQL.
    Returns a list in the format [{'username': '...', 'email': '...'}, ...].

    Returns:
        List[Dict[str, str]]: A list of unique users
    """
    # Select both username and email, and group by both to handle users with the same name but different emails
    sql = "SELECT DISTINCT username, email FROM chat_history ORDER BY username ASC, email ASC"
    users = []

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            users = results  # Results are already in the required dictionary list format
        logger.info(f"Got {len(users)} unique user combinations (username/email)")
        return users
    except <PERSON>rror as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting unique user list: {e}", exc_info=True)
        return []

def get_conversation_count_by_user() -> Dict[str, int]:
    """
    Get the count of unique conversations for each user (identified by email) from MySQL.
    Returns a dictionary in the format {email: count, ...}.

    Returns:
        Dict[str, int]: A dictionary mapping emails to conversation counts
    """
    # Group by email and count distinct conversations
    sql = """
        SELECT email, COUNT(DISTINCT conversation_id) as count
        FROM chat_history
        GROUP BY email
        ORDER BY email ASC
    """
    user_counts = {}

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            for row in results:
                user_counts[row['email']] = row['count']
        logger.info(f"Got conversation counts for {len(user_counts)} users")
        return user_counts
    except Error as e:
        # Error already logged
        return {}
    except Exception as e:
        logger.error(f"Unexpected error getting user conversation counts: {e}", exc_info=True)
        return {}


def get_top_users(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                 filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get the top users with the most queries within a specified time range.

    Args:
        limit (int, optional): Maximum number of users to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top users with their query and conversation counts
    """
    sql = """
        SELECT
            username,
            email,
            COUNT(CASE WHEN role = 'user' THEN 1 ELSE NULL END) AS query_count,
            COUNT(DISTINCT conversation_id) AS conversation_count
        FROM chat_history
        WHERE 1=1
    """
    params = []

    # Add time range filters if provided
    if start_time is not None:
        sql += " AND timestamp >= %s"
        params.append(start_time)
    if end_time is not None:
        sql += " AND timestamp <= %s"
        params.append(end_time)

    # Add admin filter if requested
    if filter_admin:
        # Get admin users from utility function
        admin_users = get_admin_users()

        if admin_users:
            placeholders = ','.join(['%s'] * len(admin_users))
            sql += f" AND username NOT IN ({placeholders})"
            params.extend(admin_users)

    # Group by user and order by query count
    sql += """
        GROUP BY username, email
        ORDER BY query_count DESC
        LIMIT %s
    """
    params.append(limit)

    try:
        results = execute_db_query(sql, tuple(params), fetch='all')
        if results:
            logger.info(f"Got top {len(results)} users by query count")
            return results
        else:
            logger.warning("No users found for top users query")
            return []
    except Error as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting top users: {e}", exc_info=True)
        return []


def _load_agent_configs() -> List[Dict[str, Any]]:
    """
    Load all available agent configurations from the data_fetcher_bot_config directory.

    Returns:
        List[Dict[str, Any]]: A list of agent configurations
    """
    agent_configs = []

    # Get all YAML files from the data_fetcher_bot_config directory
    config_files = list_resources('data_fetcher_bot_config', '.yml')

    for config_file in config_files:
        # Load the configuration from the YAML file
        yaml_content = load_resource('data_fetcher_bot_config', config_file)
        if yaml_content:
            try:
                config = yaml.safe_load(yaml_content)
                if config and 'agent_name' in config:
                    agent_configs.append(config)
            except Exception as e:
                logger.error(f"Error parsing YAML config file {config_file}: {e}")

    return agent_configs


def get_top_agents(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                  filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get the top agents with the most conversations within a specified time range.

    Args:
        limit (int, optional): Maximum number of agents to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top agents with their conversation counts
    """
    # Load all available agent configurations
    agent_configs = _load_agent_configs()
    agent_names = [config.get('agent_name') for config in agent_configs if config.get('agent_name')]

    # Add default agent names if no configurations are found
    if not agent_names:
        agent_names = ['sales_order_analytics', 'warehouse_and_fulfillment']

    logger.info(f"Found {len(agent_names)} agent configurations: {agent_names}")

    # First, get all assistant messages with logs that might contain agent information
    sql = """
        SELECT
            conversation_id,
            logs,
            username
        FROM chat_history
        WHERE role = 'assistant'
            AND logs IS NOT NULL
            AND logs != ''
    """
    params = []

    # Add time range filters if provided
    if start_time is not None:
        sql += " AND timestamp >= %s"
        params.append(start_time)
    if end_time is not None:
        sql += " AND timestamp <= %s"
        params.append(end_time)

    # Add admin filter if requested
    if filter_admin:
        # Get admin users from utility function
        admin_users = get_admin_users()

        if admin_users:
            placeholders = ','.join(['%s'] * len(admin_users))
            sql += f" AND username NOT IN ({placeholders})"
            params.extend(admin_users)

    try:
        results = execute_db_query(sql, tuple(params) if params else None, fetch='all')
        if not results:
            logger.warning("No assistant messages with logs found")
            return []

        # Process logs to extract agent names
        agent_conversations = {agent_name: set() for agent_name in agent_names}

        for row in results:
            logs = row.get('logs', '') or ''
            conversation_id = row.get('conversation_id')

            if not conversation_id:
                continue

            # Flag to track if we found an agent for this conversation
            agent_found = False

            # Check each known agent name in the logs and output_as_input
            for agent_name in agent_names:
                # Check in logs
                if logs and agent_name.lower() in logs.lower():
                    agent_conversations[agent_name].add(conversation_id)
                    agent_found = True
                    break


            # If we couldn't find a known agent, use a default
            if not agent_found:
                default_agent = "others"
                if default_agent not in agent_conversations:
                    agent_conversations[default_agent] = set()
                agent_conversations[default_agent].add(conversation_id)

        # Convert to list of dictionaries with counts and remove agents with zero conversations
        agent_stats = [
            {
                "agent_name": agent_name,
                "conversation_count": len(conversations)
            }
            for agent_name, conversations in agent_conversations.items()
            if len(conversations) > 0
        ]

        # Sort by conversation count and limit
        agent_stats.sort(key=lambda x: x["conversation_count"], reverse=True)
        agent_stats = agent_stats[:limit]

        logger.info(f"Got top {len(agent_stats)} agents by conversation count")
        return agent_stats

    except Error:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting top agents: {e}", exc_info=True)
        return []
