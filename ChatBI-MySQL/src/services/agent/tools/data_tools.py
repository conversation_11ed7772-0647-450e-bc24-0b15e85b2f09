"""
Data-related tools for agent bots.
"""

from typing import Tuple, List, Dict, Any
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.services.agent.tools.feishu_tools import upload_sql_result_to_feishu_if_needed
from src.services.xianmudb.query_service import execute_business_query
from src.services.xianmudb.metadata_service import (
    get_table_sample_data as get_table_sample_data_real,
)
from src.models.query_result import SQLQueryResult
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager

async def fetch_mysql_sql_result(
    wrapper: RunContextWrapper[UserInfo], sql: str, description: str
) -> <PERSON><PERSON>[SQLQueryResult, str]:
    """使用SQL来查询MySQL数据库，返回查询结果(字典格式)和对查询的描述。

    Args:
        sql: 要执行的 SQL 查询语句。
        description: 对 SQL 查询的简短描述。
    """
    # 调用 xianmudb 服务执行查询
    result = execute_business_query(sql)
    if result.success:
        # Pass user_info from the wrapper context
        result = upload_sql_result_to_feishu_if_needed(
            sql_result=result, sql_descriptoin=description, user_info=wrapper.context
        )
        return result, description
    else:
        # Return None for the result part of the tuple if the query failed
        return None, f"{description} 执行失败: {result.error}"


def get_sales_manager_team_members(bd_id = None, bd_name = None) -> List[Any]:
    """
    根据销售主管的ID或者名字，获取销售主管（M1或者M2）的团队成员，包括直接下属和间接下属的销售代表。

    参数:
        bd_id: 销售主管的ID。【注意，千万不可编造ID】
        bd_name: 销售主管的名字。【注意，千万不可编造名字】
        如果 bd_id 和 bd_name 都提供，优先使用 bd_id。

    返回:
        list: 销售主管的所有团队成员bd_id的列表。
    """
    if bd_id is None and bd_name is None:
        return []
    
    # 尝试将bd_id转换为整数
    query_str = f"bd_name = '{bd_name}'"
    try:
        if bd_id is not None:
            bd_id_int = int(bd_id)
            query_str = f"bd_id = {bd_id_int}"
    except (ValueError, TypeError):
        # 如果bd_id无法转换为整数，则使用bd_name
        logger.error(f"bd_id无法转换为整数: {bd_id}, 使用bd_name: {bd_name}")
            
    sql = f"""SELECT `bd_id` FROM `crm_bd_org` WHERE `parent_id` IN (
  SELECT id FROM crm_bd_org WHERE {query_str})
UNION ALL
SELECT `bd_id` FROM `crm_bd_org` WHERE `parent_id` IN (
  SELECT id FROM `crm_bd_org`
  WHERE `parent_id` IN (SELECT id FROM crm_bd_org WHERE {query_str}))"""
    result = execute_business_query(sql)
    if result.success:
        return result.data
    else:
        return []


def get_table_sample_data(table_name: str, sample_size: int = 2) -> SQLQueryResult:
    """
    获取指定表的样本数据。

    参数:
        table_name (str): 表名。
        sample_size (int): 样本大小，默认为2。

    使用示例:
        # 获取`orders`表的示例数据
        order_sample_data = get_table_sample_data('orders')

        # 获取最新的`merchants`表的示例数据
        order_sample_data = get_table_sample_data('merchants', 5)

    返回:
        dict: {"columns": columns, "data": list, "error": None}. list包含示例数据, columns为列名列表
    """
    return get_table_sample_data_real(table_name, sample_size)


tool_manager.register_as_function_tool(fetch_mysql_sql_result)
tool_manager.register_as_function_tool(get_sales_manager_team_members)
tool_manager.register_as_function_tool(get_table_sample_data)
