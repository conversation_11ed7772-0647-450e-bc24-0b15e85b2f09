"""
Chat quality service module.

This module provides business logic for managing chatbot quality and bad cases.
"""

import functools
from typing import Optional

from src.utils.logger import logger
from src.repositories.chatbi.bad_case import (
    mark_conversation_as_bad_case,
    get_conversation_bad_case_status,
)


def feishu_notification_decorator(func):
    """
    装饰器：在mark_bad_case执行成功后发送飞书机器人消息到群聊

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 执行原始函数
        result = func(*args, **kwargs)

        # 如果执行成功，则发送通知
        if result:
            # 获取参数值，支持位置参数和关键字参数
            conversation_id = args[0] if len(args) > 0 else kwargs.get('conversation_id')
            is_bad_case = args[1] if len(args) > 1 else kwargs.get('is_bad_case', True)

            # 只有在标记为bad case时才发送通知
            if is_bad_case is True:
                try:
                    # 导入发送通知的函数（延迟导入避免循环依赖）
                    from src.services.feishu.message_apis import send_bad_case_notification

                    # 尝试获取用户名（如果有的话）
                    user_name = args[2] if len(args) > 2 else kwargs.get('user_name', None)

                    # 发送通知
                    notification_sent = send_bad_case_notification(conversation_id, user_name)
                    if notification_sent:
                        logger.info(f"已发送Bad Case通知到群聊，对话ID: {conversation_id}")
                    else:
                        logger.warning(f"发送Bad Case通知失败，对话ID: {conversation_id}")

                except Exception as e:
                    logger.error(f"发送Bad Case通知时发生异常: {str(e)}", exc_info=True)

        return result

    return wrapper

@feishu_notification_decorator
def mark_bad_case(conversation_id: str, is_bad_case: bool = True, user_name: str = None) -> bool:
    """
    Mark or unmark a conversation as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        is_bad_case (bool, optional): Whether to mark as bad case (True) or unmark (False). Defaults to True.
        user_name (str, optional): The name of the user who marked the bad case. Used for notification. Defaults to None.

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    action = "marked" if is_bad_case else "unmarked"
    logger.info(f"User {action} conversation {conversation_id} as bad case, user_name: {user_name}")
    return mark_conversation_as_bad_case(conversation_id, is_bad_case)

def is_bad_case(conversation_id: str, username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """
    Check if a conversation is marked as a bad case.

    Args:
        conversation_id (str): The ID of the conversation
        username (str, optional): The username to filter by. Defaults to None.
        email (str, optional): The email to filter by. Defaults to None.

    Returns:
        bool: True if the conversation is marked as a bad case, False otherwise
    """
    logger.info(f"Checking if conversation {conversation_id} is a bad case")
    return get_conversation_bad_case_status(conversation_id, username, email)
