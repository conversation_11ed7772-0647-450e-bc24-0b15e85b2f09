"""
Dashboard service module.

This module provides business logic for dashboard-related operations,
including authentication, statistics, and data retrieval for the dashboard.
"""

from functools import wraps
from typing import List, Dict, Any, Optional

from flask import session, request, redirect, url_for

from src.repositories.chatbi.dashboard.history import (
    query_conversations_for_dashboard,
    get_filtered_conversation_count_for_dashboard
)
from src.repositories.chatbi.dashboard.statistics import get_dashboard_stats, get_daily_usage_data, get_daily_users_data
from src.repositories.chatbi.dashboard.users import (
    get_unique_users, get_conversation_count_by_user,
    get_top_users, get_top_agents
)
from src.utils.logger import logger
from src.utils.env_utils import load_env, get_admin_users

# Load environment variables
load_env()

# Load admin users from environment variable
ADMIN_USERS = set(get_admin_users())

logger.info(f"Dashboard Admin Users: {ADMIN_USERS}")


# --- Dashboard Service Functions ---

def get_daily_users_statistics(days: int = 30) -> Dict[str, list]:
    """
    Get daily unique user count for the specified number of days.
    Args:
        days (int, optional): The number of days to get data for. Defaults to 30.
    Returns:
        Dict[str, List]: A dictionary containing dates and user counts
    """
    logger.info(f"Dashboard service: Requesting daily users data for the past {days} days")
    return get_daily_users_data(days)


def get_conversations_for_dashboard(filters: Optional[Dict[str, Any]] = None,
                                   limit: int = 20,
                                   offset: int = 0) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get filtered chatbot conversations for the dashboard, grouped by conversation_id.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.
        limit (int, optional): Maximum number of conversations to return. Defaults to 20.
        offset (int, optional): Number of conversations to skip. Defaults to 0.

    Returns:
        Dict[str, List[Dict[str, Any]]]: A dictionary of conversations, keyed by conversation_id
    """
    logger.info(f"Dashboard service: Requesting conversations with filters: {filters}, limit: {limit}, offset: {offset}")
    return query_conversations_for_dashboard(filters, limit, offset)


def get_filtered_conversation_count(filters: Optional[Dict[str, Any]] = None) -> int:
    """
    Get the count of conversations matching the specified filters.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.

    Returns:
        int: The count of conversations matching the filters
    """
    logger.info(f"Dashboard service: Requesting conversation count with filters: {filters}")
    return get_filtered_conversation_count_for_dashboard(filters)


def get_unique_users_list() -> List[Dict[str, str]]:
    """
    Get a list of all unique users.

    Returns:
        List[Dict[str, str]]: A list of unique users
    """
    logger.info("Dashboard service: Requesting unique users list")
    return get_unique_users()


def get_conversation_count_by_user_list() -> Dict[str, int]:
    """
    Get the count of unique conversations for each user.

    Returns:
        Dict[str, int]: A dictionary mapping emails to conversation counts
    """
    logger.info("Dashboard service: Requesting conversation counts by user")
    return get_conversation_count_by_user()


def get_dashboard_statistics(start_time: Optional[int] = None, end_time: Optional[int] = None,
                           filter_admin: bool = False) -> Dict[str, int]:
    """
    Get dashboard statistics for a specified time range.

    Args:
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        Dict[str, int]: A dictionary containing statistics
    """
    logger.info(f"Dashboard service: Requesting dashboard statistics with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")
    return get_dashboard_stats(start_time, end_time, filter_admin)


def get_daily_usage_statistics(days: int = 30) -> Dict[str, List]:
    """
    Get daily usage data for the specified number of days.

    Args:
        days (int, optional): The number of days to get data for. Defaults to 30.

    Returns:
        Dict[str, List]: A dictionary containing dates, query counts, and bad case conversation counts
    """
    logger.info(f"Dashboard service: Requesting daily usage data for the past {days} days")
    return get_daily_usage_data(days)


def get_top_users_statistics(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                           filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get statistics for top users with the most queries within a specified time range.

    Args:
        limit (int, optional): Maximum number of users to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top users with their query and conversation counts
    """
    logger.info(f"Dashboard service: Requesting top {limit} users with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")
    return get_top_users(limit, start_time, end_time, filter_admin)


def get_top_agents_statistics(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                            filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get statistics for top agents with the most conversations within a specified time range.

    Args:
        limit (int, optional): Maximum number of agents to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top agents with their conversation counts
    """
    logger.info(f"Dashboard service: Requesting top {limit} agents with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")
    return get_top_agents(limit, start_time, end_time, filter_admin)


# --- Dashboard Authentication ---

def admin_required(f):
    """
    Decorator that checks if the user is logged in and has admin privileges.
    If not, redirects to login or returns a 403 error.
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First, check if the user is logged in via Feishu
        if "user_info" not in session:
            logger.warning("Admin access denied: User not logged in via Feishu.")
            # Redirect to Feishu login, passing the expected dashboard URL
            return redirect(url_for('login_route', next=request.url))

        user_info = session.get("user_info")
        username = user_info.get("name")

        # Then, check if the logged-in user is in the ADMIN_USERS list
        if not username or username not in ADMIN_USERS:
            logger.warning(f"Admin access denied for user: {username}. Not in ADMIN_USERS list.")
            # Return 403 Forbidden with a message
            return "您没有访问dashboard的权限", 403

        # If both checks pass, continue executing the decorated function
        return f(*args, **kwargs)

    return decorated_function
