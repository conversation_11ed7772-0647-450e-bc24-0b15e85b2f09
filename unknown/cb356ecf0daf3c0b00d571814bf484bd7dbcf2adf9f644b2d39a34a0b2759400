"""
为agent机器人提供飞书集成工具，用于将SQL查询结果上传到飞书云文档，并根据内容大小动态调整展示的行数。
"""
import os
from datetime import datetime

from src.utils.logger import logger
from src.models.user_info_class import UserInfo
from src.models.query_result import SQLQueryResult
from src.services.feishu.import_csv_to_feishu_bitable import create_bitable_and_upload_csv

# 配置
min_rows = int(os.getenv("MIN_ROWS_TO_IMPORT", 20))  # 默认最小导入行数为20行
min_rows_to_cutoff = int(os.getenv("MIN_ROWS_TO_CUTOFF", 400))  # 默认最小截断行数为400行
max_content_size = int(os.getenv("MAX_CONTENT_TO_CUTOFF", 16000))  # 默认最大内容大小为16000字节


def upload_sql_result_to_feishu_if_needed(
        sql_result: SQLQueryResult,
        sql_descriptoin: str,
        user_info: UserInfo
) -> SQLQueryResult:
    """
    将SQL查询结果上传到飞书云文档，并根据内容大小动态调整展示的行数。

    当查询结果行数超过MIN_ROWS_TO_IMPORT（默认20行）时，会将完整结果上传到飞书云文档。
    同时，根据内容大小动态计算需要在界面上展示的行数，避免内容过大导致性能问题：
    - 如果估算的内容大小超过MAX_CONTENT_TO_CUTOFF（默认16000字节），会动态计算合适的展示行数
    - 如果内容大小在限制范围内，则最多展示MIN_ROWS_TO_CUTOFF（默认400行）
    - 对于包含逗号、引号等特殊字符的数据，会进行正确的CSV转义处理

    Args:
        sql_result: SQL查询结果对象，包含列名和数据行
        sql_descriptoin: SQL查询的描述，用于生成飞书文档名称
        user_info: 用户信息，包含用户名和访问令牌等

    Returns:
        SQLQueryResult: 处理后的SQL查询结果对象，可能包含截断的数据和飞书文档链接
    """
    columns = sql_result.columns
    rows = sql_result.data

    # 检查数据是否为空
    if not rows:
        sql_result.message = "查询结果为空。"
        sql_result.success = True
        return sql_result

    date_of_now = datetime.now().strftime("%Y-%m-%d")
    # 限制名称长度为100个字符以免超出限制
    name = f"{date_of_now}_{sql_descriptoin[:100]}"

    if len(rows) >= min_rows:
        try:
            # 构建CSV内容，正确处理包含逗号的字符串
            def escape_csv_field(field):
                if field is None:
                    return ""
                field_str = str(field)
                # 如果字段包含逗号、引号或换行符，则需要用引号包围并处理内部引号
                if ',' in field_str or '"' in field_str or '\n' in field_str:
                    # 将字段中的引号替换为两个引号（CSV标准转义方式）
                    field_str = field_str.replace('"', '""')
                    # 用引号包围整个字段
                    return f'"{field_str}"'
                return field_str

            # 处理表头和数据行
            header_row = ",".join(map(escape_csv_field, columns))
            data_rows = [",".join(map(escape_csv_field, row)) for row in rows]

            # 动态计算需要截断的行数
            # 首先计算每行的平均大小
            total_rows = len(data_rows)
            if total_rows > 0:
                # 计算前100行（或全部行，如果少于100行）的平均大小
                sample_size = min(100, total_rows)
                sample_content = "\n".join(data_rows[:sample_size])
                avg_row_size = len(sample_content.encode('utf-8')) / sample_size

                # 估算全部内容的大小
                estimated_full_size = len(header_row.encode('utf-8')) + (avg_row_size * total_rows)

                # 如果估算大小超过最大限制，计算需要保留的行数
                display_rows = total_rows
                if estimated_full_size > max_content_size:
                    # 计算在最大内容大小限制下可以保留的行数
                    # 预留header的空间和一些缓冲区
                    header_size = len(header_row.encode('utf-8'))
                    available_size = max_content_size - header_size - 100  # 100字节作为缓冲区
                    display_rows = int(available_size / avg_row_size)

                    # 确保至少显示一些行，但不超过min_rows_to_cutoff
                    display_rows = max(10, min(display_rows, min_rows_to_cutoff))
                    logger.info(f"内容大小估算: {estimated_full_size}字节，超过限制{max_content_size}字节，将截断至{display_rows}行")
                else:
                    # 如果内容大小在限制范围内，使用默认的min_rows_to_cutoff
                    display_rows = min(min_rows_to_cutoff, total_rows)
                    logger.info(f"内容大小估算: {estimated_full_size}字节，在限制范围内，将显示{display_rows}行")
            else:
                display_rows = 0

            # 生成最终的CSV内容
            csv_content = "\n".join([header_row] + data_rows)

            logger.info(f"即将为用户:{user_info.user_name}创建云文档:{name}")
            result = create_bitable_and_upload_csv(
                csv_content=csv_content,
                access_token=user_info.access_token,
                name=name,
            )

            # 截断数据以便展示
            sql_result.data = sql_result.data[:display_rows]

            if result and result.status == "success":
                sql_result.message = f"查询结果行数:{len(rows)}，现展示了前{len(sql_result.data)}行，请一定要告知用户完整结果已上传至飞书文档：[{name}]({result.url})"
                sql_result.success = True
                sql_result.feishu_url = result.url
                sql_result.feishu_app_token = getattr(result, 'app_token', None)
                sql_result.feishu_table_id = getattr(result, 'table_id', None)
            else:
                sql_result.message = f"查询结果上传失败"
                sql_result.error = f"查询结果上传失败"
        except Exception as e:
            logger.error(f"上传查询结果到飞书时出错: {str(e)}")
            sql_result.message = f"查询结果行数:{len(rows)}，但上传过程中出错"
            sql_result.error = f"上传过程错误: {str(e)}"
    else:
        sql_result.message = f"查询结果行数:{len(rows)}，已经展示完毕。"
        sql_result.success = True

    return sql_result
