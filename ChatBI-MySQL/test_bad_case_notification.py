#!/usr/bin/env python3
"""
测试Bad Case通知功能的脚本

这个脚本用于测试装饰器和飞书通知功能是否正常工作。
"""

import os
import sys
import uuid
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

def test_bad_case_notification():
    """测试Bad Case通知功能"""
    print("🧪 开始测试Bad Case通知功能...")
    
    # 导入需要的模块
    try:
        from src.services.chatbot.bad_case_service import mark_bad_case
        from src.services.feishu.message_apis import send_bad_case_notification, BAD_CASE_NOTIFICATION_CHAT_ID
        print("✅ 成功导入所需模块")
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    
    # 检查环境变量配置
    print(f"📋 Bad Case通知群聊ID: {BAD_CASE_NOTIFICATION_CHAT_ID or '未配置'}")
    
    # 生成测试对话ID
    test_conversation_id = f"test_{uuid.uuid4().hex[:8]}"
    test_user_name = "测试用户"
    
    print(f"🔍 测试对话ID: {test_conversation_id}")
    print(f"👤 测试用户名: {test_user_name}")
    
    # 测试1: 直接测试通知函数
    print("\n📤 测试1: 直接测试通知函数...")
    try:
        result = send_bad_case_notification(test_conversation_id, test_user_name)
        if result:
            print("✅ 通知发送成功")
        else:
            print("⚠️ 通知发送失败（可能是未配置群聊ID）")
    except Exception as e:
        print(f"❌ 通知发送异常: {e}")
    
    # 测试2: 测试装饰器功能（模拟标记Bad Case）
    print("\n🎭 测试2: 测试装饰器功能...")
    try:
        # 注意：这里不会真正标记到数据库，因为我们使用的是测试ID
        # 但装饰器应该会尝试发送通知
        result = mark_bad_case(
            conversation_id=test_conversation_id,
            is_bad_case=True,
            user_name=test_user_name
        )
        print(f"📊 mark_bad_case 返回结果: {result}")
        print("✅ 装饰器功能测试完成")
    except Exception as e:
        print(f"❌ 装饰器测试异常: {e}")
    
    print("\n🎉 测试完成！")
    print("\n📝 使用说明:")
    print("1. 要启用Bad Case通知功能，请在.env文件中配置 BAD_CASE_NOTIFICATION_CHAT_ID")
    print("2. 群聊ID格式通常为: oc_xxxxxxxxxxxxxxxxxxxxxxxx")
    print("3. 确保飞书机器人已加入目标群聊并有发送消息权限")
    
    return True

if __name__ == "__main__":
    test_bad_case_notification()
