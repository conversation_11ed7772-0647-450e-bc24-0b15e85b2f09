CREATE TABLE `large_area` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `large_area_no` int(11) DEFAULT NULL COMMENT '运营大区编号。',
  `large_area_name` varchar(50) DEFAULT NULL COMMENT '运营大区名称，如：‘上海大区’,‘杭州大区’,‘华南大区’等',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_large_area_no` (`large_area_no`)
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COMMENT='运营大区信息，一个运营大区通常会包含多个运营服务区(关联查询area表， 通过area.large_area_no = large_area.large_area_no)。';