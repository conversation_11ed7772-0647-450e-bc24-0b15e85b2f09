"""
Main application file for ChatBI-MySQL.
"""
import argparse
import os
import threading

from dotenv import load_dotenv
from flask import Flask
from werkzeug.middleware.proxy_fix import ProxyFix

from src.api import register_routes
from src.services.feishu.client import start_feishu_client
from src.utils.logger import logger

# --- Configuration ---
load_dotenv()
APPLICATION_ROOT = os.getenv("APPLICATION_ROOT", "/crm-chatbi")
ENABLE_BOT_MESSAGE_PROCESSING = os.getenv("ENABLE_BOT_MESSAGE_PROCESSING",
                                          "False").lower() == "true"
logger.info(f"APPLICATION_ROOT:{APPLICATION_ROOT}")

APP_NAME = "CRM-ChatBI-MySQL"
app = Flask(APP_NAME,
            static_folder="src/static",  # Updated path to static folder
            template_folder="src/templates"  # Updated path to templates folder
            )
# Apply ProxyFix to handle headers from Nginx
app.wsgi_app = ProxyFix(
    app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1
)
app.config["SEND_FILE_MAX_AGE_DEFAULT"] = 86400
app.secret_key = APP_NAME  # Used for session encryption
MEGABYTE = (2 ** 10) ** 2
app.config["MAX_CONTENT_LENGTH"] = None
app.config["MAX_FORM_MEMORY_SIZE"] = 20 * MEGABYTE

# Register all API routes
register_routes(app)

# --- Main Application Start ---
if __name__ == "__main__":
    # Create argument parser
    parser = argparse.ArgumentParser(description="Run Flask application")

    # Add port parameter, default value is 5700
    parser.add_argument("--port", type=int, default=5700, help="Listen port")

    # Add debug mode parameter, default is True
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument(
        "--no-debug", action="store_false", dest="debug", help="Disable debug mode"
    )
    parser.set_defaults(debug=False)

    # Parse command line arguments
    args = parser.parse_args()

    # Start Feishu client in a separate thread
    if ENABLE_BOT_MESSAGE_PROCESSING:
        feishu_thread = threading.Thread(target=start_feishu_client, daemon=True)
        feishu_thread.start()
        logger.info("Feishu client WebSocket connection started in background thread")

    # Run in development environment for debugging
    app.run(host="0.0.0.0", port=args.port, debug=args.debug)
