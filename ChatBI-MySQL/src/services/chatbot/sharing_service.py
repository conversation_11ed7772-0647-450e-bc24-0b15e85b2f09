"""
Conversation sharing service module.

This module provides business logic for sharing conversations.
"""

from typing import Dict, Any, Optional, List

from repositories.chatbi.conversation_sharing import create_share_link, get_shared_conversation
from repositories.chatbi.history import load_conversation
from src.utils.logger import logger
def share_conversation(username: str, email: str, conversation_id: str) -> Optional[str]:
    """
    Share a conversation by creating a share link.
    
    Args:
        username (str): The username of the user sharing the conversation
        email (str): The email of the user sharing the conversation
        conversation_id (str): The ID of the conversation to share
        
    Returns:
        Optional[str]: The share ID if successful, None otherwise
    """
    logger.info(f"Sharing conversation {conversation_id} for {username} ({email})")
    return create_share_link(username, email, conversation_id)

def get_shared_conversation_info(share_id: str) -> Optional[Dict[str, Any]]:
    """
    Get information about a shared conversation.
    
    Args:
        share_id (str): The ID of the share
        
    Returns:
        Optional[Dict[str, Any]]: Information about the shared conversation if found, None otherwise
    """
    logger.info(f"Getting shared conversation info for share ID {share_id}")
    return get_shared_conversation(share_id)

def get_shared_conversation_messages(share_id: str) -> List[Dict[str, Any]]:
    """
    Get the messages in a shared conversation.
    
    Args:
        share_id (str): The ID of the share
        
    Returns:
        List[Dict[str, Any]]: The messages in the shared conversation
    """
    logger.info(f"Getting shared conversation messages for share ID {share_id}")
    
    # First, get the share information
    share_info = get_shared_conversation(share_id)
    if not share_info:
        logger.warning(f"Share ID {share_id} not found")
        return []
    
    # Then, get the conversation messages
    conversation_id = share_info.get('conversation_id')
    owner_username = share_info.get('owner_username')
    owner_email = share_info.get('owner_email')
    
    if not conversation_id or not owner_username or not owner_email:
        logger.warning(f"Invalid share information for share ID {share_id}")
        return []
    
    # Load the conversation messages
    return load_conversation(owner_username, owner_email, conversation_id)
