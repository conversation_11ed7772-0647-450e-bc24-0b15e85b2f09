/**
 * Welcome Module Styles
 *
 * Defines styles for the welcome module, following Apple/OpenAI design aesthetics.
 * Features a minimalist yet sophisticated approach with careful typography and spacing.
 */

/* Welcome container - 确保欢迎模块在消息区域中居中 */
.welcome-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Welcome content container */
.welcome-appear-animation {
  max-width: 36rem;
  padding: 1rem;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 标题动画 */
.welcome-title {
  opacity: 0;
  animation: fadeSlideUp 0.8s ease-out forwards;
  transform: translateY(10px);
}

/* 副标题动画 - 延迟出现 */
.welcome-subtitle {
  opacity: 0;
  animation: fadeSlideUp 0.8s ease-out 1.2s forwards; /* 1.2秒延迟 */
  transform: translateY(10px);
}

/* 暗色模式下的标题颜色调整 */
[data-theme="dark"] .welcome-title {
  color: #ffffff; /* 标题使用全白色 */
}

@keyframes fadeSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .welcome-appear-animation h2 {
    font-size: 1.5rem;
  }

  .welcome-appear-animation p {
    font-size: 1rem;
  }
}
