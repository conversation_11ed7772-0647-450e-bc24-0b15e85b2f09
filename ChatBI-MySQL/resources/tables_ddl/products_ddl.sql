CREATE TABLE `products` (
  `pd_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT '商品ID，主键，自增长',
  `category_id` int(11) DEFAULT NULL COMMENT '商品分类ID，关联category.id表',
  `pd_name` varchar(255) DEFAULT NULL COMMENT '商品名称，如"封口膜"',
  `create_time` datetime DEFAULT NULL COMMENT '商品上架时间',
  `expire_time` datetime DEFAULT NULL COMMENT '商品下架时间',
  `outdated` int(11) NOT NULL DEFAULT '0' COMMENT '商品状态：-1:上新中, 0:有效, 1:已删除',
  `storage_location` tinyint(4) DEFAULT '0' COMMENT '仓储区域：0-未分类 1-冷冻 2-冷藏 3-常温',
  `origin` int(11) DEFAULT NULL COMMENT '商品产地ID',
  `storage_method` varchar(255) DEFAULT NULL COMMENT '存储方式说明',
  `picture_path` varchar(1255) DEFAULT NULL COMMENT '商品主图URL',
  `quality_time` int(10) NOT NULL DEFAULT '0' COMMENT '保质期时长，如24表示24个月',
  `quality_time_unit` varchar(30) NOT NULL DEFAULT 'day' COMMENT '保质期单位，如"month"表示月',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `create_type` int(11) DEFAULT '0' COMMENT '上新类型：0-平台 1-大客户 2-其他',
  `real_name` varchar(50) DEFAULT NULL COMMENT '商品实物名称，如"锦凯塑料咖啡杯"',
  `audit_status` int(11) DEFAULT '1' COMMENT '审核状态：0-待上新 1-上新成功 2-上新失败',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`pd_id`),
  KEY `products_to_category_fk` (`category_id`),
  KEY `products_to_brand_fk` (`brand_id`),
  KEY `products_pdname_index` (`pd_name`)
) ENGINE=InnoDB AUTO_INCREMENT=15534 DEFAULT CHARSET=utf8 COMMENT='SPU基础信息表，存储平台所有SPU的基本信息和状态。每个products(pd_id)可能有多个inventory(SKU)';