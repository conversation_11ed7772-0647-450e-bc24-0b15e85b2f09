"""
数据库连接管理模块。

该模块提供用于管理数据库连接和执行查询的功能。
它处理连接池、查询执行和错误处理。
"""

import mysql.connector
from mysql.connector import Error, pooling
import os
from dotenv import load_dotenv
from src.utils.logger import logger
from typing import List, Dict, Any, Optional, Tuple, Union
from src.db.database_enum import Database

# 加载环境变量
load_dotenv()

# --- 数据库配置常量 ---
# 通用设置
DB_CHARSET = os.getenv("DB_CHARSET", "utf8mb4") # 使用 utf8mb4 支持表情符号
DB_EXECUTE_TIMEOUT_MS = int(os.getenv("DB_EXECUTE_TIMEOUT_MS", 300000)) # 默认 5 分钟（毫秒）

# ChatBI 数据库 (项目自身数据)
CHATBI_DB_HOST = os.getenv("CHATBI_MYSQL_HOST", "mysql-xm.summerfarm.net")
CHATBI_DB_PORT = int(os.getenv("CHATBI_MYSQL_PORT", 3308))
CHATBI_DB_USER = os.getenv("CHATBI_MYSQL_USER", "qa")
CHATBI_DB_PASSWORD = os.getenv("CHATBI_MYSQL_PASSWORD", "xianmu619")
CHATBI_DB_NAME = os.getenv("CHATBI_MYSQL_DATABASE", "chatbi_test")
CHATBI_POOL_NAME = "chatbi_pool"
CHATBI_POOL_SIZE = int(os.getenv("CHATBI_MYSQL_POOL_SIZE", 5)) # 可配置的池大小
CHATBI_CONNECT_TIMEOUT = int(os.getenv("CHATBI_CONNECT_TIMEOUT", 10)) # 连接超时（秒）

# 业务数据库 (业务表，如订单、商户等)
BUSINESS_DB_HOST = os.getenv("DB_HOST", "rr-bp19zkvz53m17h8qsmo.mysql.rds.aliyuncs.com")
BUSINESS_DB_PORT = int(os.getenv("DB_PORT", 3306))
BUSINESS_DB_USER = os.getenv("DB_USER", "xianmu_ai")
BUSINESS_DB_PASSWORD = os.getenv("DB_PASSWORD", "Xianmu_ai")
BUSINESS_DB_NAME = os.getenv("DB_NAME", "xianmudb")
BUSINESS_POOL_NAME = "business_pool"
BUSINESS_POOL_SIZE = int(os.getenv("BUSINESS_MYSQL_POOL_SIZE", 20)) # 可配置的池大小
BUSINESS_CONNECT_TIMEOUT = int(os.getenv("DB_CONNECT_TIMEOUT", 3)) # 连接超时（秒）

# --- 全局连接池变量 ---
chatbi_db_pool: Optional[pooling.MySQLConnectionPool] = None
business_db_pool: Optional[pooling.MySQLConnectionPool] = None

# --- 数据库连接池初始化 (模块加载时执行) ---

def _initialize_pool(pool_config: Dict[str, Any]) -> Optional[pooling.MySQLConnectionPool]:
    """内部辅助函数，用于初始化单个连接池"""
    try:
        pool_name = pool_config["pool_name"]
        db_name = pool_config["database"]
        logger.info(f"Initializing MySQL connection pool for {pool_name}: host={pool_config['host']}, port={pool_config['port']}, user={pool_config['user']}, db={db_name}, pool_size={pool_config['pool_size']}")
        pool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name=pool_name,
            pool_size=pool_config["pool_size"],
            host=pool_config["host"],
            port=pool_config["port"],
            user=pool_config["user"],
            password=pool_config["password"],
            database=db_name, # 初始连接时指定数据库
            charset=DB_CHARSET,
            auth_plugin='mysql_native_password',
            autocommit=True, # 在池级别设置 autocommit=True，需要时显式控制事务
            connection_timeout=pool_config["connection_timeout"]
        )
        logger.info(f"MySQL connection pool '{pool_name}' for database '{db_name}' initialized successfully")
        return pool
    except Error as e:
        logger.error(f"Failed to create MySQL connection pool '{pool_config.get('pool_name', 'unknown')}' for database '{pool_config.get('database', 'unknown')}': {e}", exc_info=True)
        # 在启动时遇到错误，最好抛出异常，以便应用程序知道存在问题
        # raise e # 根据需要决定是否在初始化失败时停止应用
        return None # 或者返回 None，让应用尝试稍后处理

# 初始化 ChatBI 连接池
chatbi_pool_config = {
    "pool_name": CHATBI_POOL_NAME,
    "pool_size": CHATBI_POOL_SIZE,
    "host": CHATBI_DB_HOST,
    "port": CHATBI_DB_PORT,
    "user": CHATBI_DB_USER,
    "password": CHATBI_DB_PASSWORD,
    "database": CHATBI_DB_NAME,
    "connection_timeout": CHATBI_CONNECT_TIMEOUT
}
chatbi_db_pool = _initialize_pool(chatbi_pool_config)

# 初始化 Business 连接池
business_pool_config = {
    "pool_name": BUSINESS_POOL_NAME,
    "pool_size": BUSINESS_POOL_SIZE,
    "host": BUSINESS_DB_HOST,
    "port": BUSINESS_DB_PORT,
    "user": BUSINESS_DB_USER,
    "password": BUSINESS_DB_PASSWORD,
    "database": BUSINESS_DB_NAME,
    "connection_timeout": BUSINESS_CONNECT_TIMEOUT
}
business_db_pool = _initialize_pool(business_pool_config)


# --- 数据库连接管理 ---

def get_db_connection(database: Union[Database, str] = Database.CHATBI) -> pooling.PooledMySQLConnection:
    """
    从连接池获取数据库连接。

    Args:
        database (Union[Database, str]): 要连接的数据库。可以是 Database.CHATBI 或 Database.BUSINESS。

    Returns:
        来自池的连接。

    Raises:
        Error: 如果相应的连接池未初始化或无法获取连接。
    """
    # 记录要连接的数据库
    db_enum_name = str(database) # e.g., "Database.CHATBI"
    logger.debug(f"Getting connection for {db_enum_name.upper()} database")

    # 根据数据库参数确定使用哪个池和配置
    db_name = BUSINESS_DB_NAME
    if database == Database.BUSINESS:
        current_pool = business_db_pool
        pool_name = BUSINESS_POOL_NAME
    else:  # 默认为 chatbi
        current_pool = chatbi_db_pool
        pool_name = CHATBI_POOL_NAME
        db_name = CHATBI_DB_NAME # 实际数据库名称

    # 检查池是否已成功初始化
    if current_pool is None:
        logger.error(f"Connection pool '{pool_name}' for {database} is not initialized.")
        raise Error(f"Connection pool '{pool_name}' for {database} is not available.")

    conn = None
    cursor = None
    try:
        logger.debug(f"Attempting to get connection from pool '{pool_name}'...")
        conn = current_pool.get_connection()

        if conn and conn.is_connected():
            logger.debug(f"Successfully obtained connection from {database} pool (pool '{pool_name}')")

            # --- 新增：获取连接后立即设置数据库上下文 ---
            try:
                cursor = conn.cursor()
                use_db_sql = f"USE `{db_name}`;" # 使用反引号以防数据库名称包含特殊字符
                logger.debug(f"Executing: {use_db_sql}")
                cursor.execute(use_db_sql)
                logger.debug(f"Database context set to '{db_name}' for the current connection.")
            except Error as e:
                logger.error(f"Failed to execute 'USE {db_name}' for the connection: {e}", exc_info=True)
                # 如果设置数据库失败，可能需要关闭连接并重新抛出异常
                if conn:
                    conn.close() # 将可能无效的连接返回池中（或关闭）
                raise Error(f"Failed to set database context to '{db_name}'") from e
            finally:
                if cursor:
                    cursor.close() # 关闭用于 USE 语句的游标
            return conn
        else:
            logger.error(f"Connection obtained from {database} pool (pool '{pool_name}') is invalid or failed. Connection object: {conn}")
            raise Error(f"Unable to get valid connection from {database} pool (pool '{pool_name}')")
    except Error as e:
        logger.error(f"Failed to get connection from {database} pool (pool '{pool_name}'): {e}", exc_info=True)
        # 如果获取连接时出错，确保 conn 不会泄露（尽管 get_connection 失败时通常不会返回 conn）
        if conn:
             conn.close() # 尝试关闭
        raise

def execute_db_query(sql: str, params: tuple = None, fetch: str = None, commit: bool = False,
                    connection: Optional[pooling.PooledMySQLConnection] = None, database: Union[Database, str] = Database.CHATBI) -> Any:
    """
    辅助函数，用于执行数据库查询或命令，处理连接获取、游标管理和错误。

    Args:
        sql (str): 要执行的 SQL 语句。
        params (tuple, optional): SQL 语句的参数。
        fetch (str, optional): 如何获取结果 ('one', 'all', 'count', None)。'count' 用于获取 rowcount。
        commit (bool): 执行后是否提交事务（仅在非自动提交模式或显式事务中有意义）。
        connection (PooledMySQLConnection, optional): 如果在事务中，传递现有连接。
        database (Union[Database, str], optional): 要连接的数据库。可以是 Database.CHATBI 或 Database.BUSINESS。默认为 Database.CHATBI。

    Returns:
        Any: 查询结果或受影响的行数，具体取决于 fetch 参数。

    Raises:
        Error: 如果数据库操作失败。
    """
    conn = None
    cursor = None
    is_external_conn = connection is not None

    try:
        # 如果没有提供外部连接，则从池中获取一个新连接
        conn = connection if is_external_conn else get_db_connection(database)

        # 注意：因为 get_db_connection 现在确保了正确的数据库上下文，
        # 这里不需要再次执行 USE database。

        # 根据 fetch 参数决定是否使用字典游标
        use_dict_cursor = fetch in ['one', 'all']
        cursor = conn.cursor(dictionary=use_dict_cursor)

        logger.debug(f"Executing SQL: {sql} | Params: {params}")
        cursor.execute(sql, params)

        result = None
        if fetch == 'one':
            result = cursor.fetchone()
        elif fetch == 'all':
            result = cursor.fetchall()
        elif fetch == 'count':
            result = cursor.rowcount  # 获取受影响的行数

        # 注意：如果连接池配置了 autocommit=True，则此处不需要 commit()，
        # 除非在显式事务中
        if commit and not conn.autocommit:  # 仅在需要时提交
            logger.debug("Committing transaction...")
            conn.commit()

        return result

    except Error as e:
        logger.error(f"Database operation failed SQL: {sql} | Params: {params} | Error: {e}", exc_info=True)
        # 如果是外部连接（事务），不回滚，让调用者处理
        if conn and not is_external_conn and not conn.autocommit:
            try:
                logger.warning("Operation failed, rolling back transaction...")
                conn.rollback()
            except Error as rb_err:
                logger.error(f"Failed to roll back transaction: {rb_err}", exc_info=True)
        raise  # 重新引发异常
    finally:
        if cursor:
            cursor.close()
        # 如果连接是内部获取的，则关闭它（返回到池中）
        if conn and not is_external_conn:
            conn.close()
            logger.debug("Database connection returned to pool")

# --- 数据库初始化 ---

def initialize_db():
    """
    初始化 MySQL 数据库，如果表不存在则创建它们。
    """
    # 检查 ChatBI 池是否可用
    if chatbi_db_pool:
        initialize_chatbi_db()
    else:
        logger.warning("ChatBI database pool is not initialized. Skipping ChatBI table creation.")

    # 检查 Business 池是否可用并尝试连接
    if business_db_pool:
        try:
            conn = get_db_connection(Database.BUSINESS)
            conn.close()
            logger.info("Successfully connected to business database via pool")
        except Error as e:
            logger.error(f"Failed to get connection from business database pool: {e}", exc_info=True)
            # 不在此处引发，因为应用程序可能仍可在没有业务数据库的情况下运行
    else:
        logger.warning("Business database pool is not initialized. Cannot check connection.")


def initialize_chatbi_db():
    """
    初始化 ChatBI 数据库，如果表不存在则创建它们。
    """
    conn = None
    cursor = None
    try:
        # 从池中获取连接进行初始化
        conn = get_db_connection(Database.CHATBI)
        # 初始化时，最好不使用自动提交以确保 DDL 的原子性
        conn.autocommit = False
        cursor = conn.cursor()

        logger.info("Starting to check and create ChatBI database tables...")

        # 创建 chat_history 表
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS chat_history (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(32) NOT NULL,
                email VARCHAR(64) NOT NULL,
                conversation_id VARCHAR(128) NOT NULL,
                role VARCHAR(10) NOT NULL CHECK(role IN ('user', 'assistant', 'prompt', 'error')),
                content TEXT CHARACTER SET {DB_CHARSET} COLLATE {DB_CHARSET}_unicode_ci NOT NULL,
                logs TEXT CHARACTER SET {DB_CHARSET} COLLATE {DB_CHARSET}_unicode_ci,
                timestamp BIGINT NOT NULL COMMENT '毫秒时间戳',
                is_bad_case TINYINT DEFAULT 0 COMMENT '是否为 bad case 0=否, 1=是',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                INDEX idx_user_convo_time (username, email, conversation_id, timestamp DESC),
                INDEX idx_convo_id (conversation_id),
                INDEX idx_timestamp (timestamp) -- 为按时间筛选/排序添加索引
            ) ENGINE=InnoDB DEFAULT CHARSET={DB_CHARSET};
        """)
        logger.info("chat_history table checked/created")

        # 创建 share_map 表
        cursor.execute(f"""
            CREATE TABLE IF NOT EXISTS share_map (
                share_id VARCHAR(128) PRIMARY KEY,
                conversation_id VARCHAR(128) NOT NULL,
                owner_username VARCHAR(32) NOT NULL,
                owner_email VARCHAR(64) NOT NULL,
                created_at BIGINT NOT NULL COMMENT '分享创建的毫秒时间戳',
                db_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                INDEX idx_owner (owner_username, owner_email),
                INDEX idx_conversation_id (conversation_id) -- 为按会话 ID 查找分享添加索引
            ) ENGINE=InnoDB DEFAULT CHARSET={DB_CHARSET};
        """)
        logger.info("share_map table checked/created")

        conn.commit()
        logger.info("Database table structure initialization/check complete")

    except Error as e:
        logger.error(f"Database initialization failed: {e}", exc_info=True)
        if conn:
            try:
                conn.rollback()  # 回滚事务
            except Error as rb_err:
                 logger.error(f"Rollback failed during initialization error handling: {rb_err}", exc_info=True)
        # 不重新引发异常，允许应用继续（可能功能受限）
        # raise e
    finally:
        if cursor:
            cursor.close()
        if conn:
            try:
                # 恢复连接的自动提交设置（如果池配置为 True）
                # 更好的做法是检查池的配置，但简单起见，我们假设默认为 True
                conn.autocommit = True
            except Error as ac_err:
                 logger.warning(f"Failed to reset autocommit on connection: {ac_err}")
            conn.close()  # 将连接返回到池中
            logger.debug("Initialization connection returned to pool")
