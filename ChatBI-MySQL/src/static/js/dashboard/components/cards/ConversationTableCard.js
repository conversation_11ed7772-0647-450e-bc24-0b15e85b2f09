/**
 * ConversationTableCard Component
 *
 * A modern, elegant table for displaying conversations
 * Uses DaisyUI table component with responsive design
 * Follows Apple/OpenAI-inspired aesthetic
 */
import { computed, inject } from 'vue';
import BaseDashboardCard from './BaseDashboardCard.js';
import {
    ExclamationTriangleIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    EyeIcon
} from '../../../utils/Icons.js';

export default {
    name: 'ConversationTableCard',
    components: {
        BaseDashboardCard
    },
    props: {
        conversations: {
            type: Array,
            required: true
        },
        isLoading: {
            type: Boolean,
            default: false
        },
        totalCount: {
            type: Number,
            default: 0
        },
        currentPage: {
            type: Number,
            default: 1
        },
        pageSize: {
            type: Number,
            default: 20
        },
        pageSizeOptions: {
            type: Array,
            default: () => [
                { value: 10, label: '10条/页' },
                { value: 20, label: '20条/页' },
                { value: 50, label: '50条/页' },
                { value: 100, label: '100条/页' }
            ]
        }
    },
    emits: ['page-change', 'page-size-change'],
    setup(props, { emit }) {
        // 注入全局模态框控制
        const conversationModal = inject('conversationModal');

        // Open conversation detail modal
        const openConversationDetail = (conversation) => {
            // In a real implementation, we would fetch the full conversation data
            // For now, we'll just use the conversation object passed in
            console.log('ConversationTableCard: openConversationDetail called with', conversation);
            conversationModal.openConversationDetail(conversation);
        };

        // Handle pagination
        const goToPage = (page) => {
            if (page !== props.currentPage) {
                emit('page-change', page);
            }
        };

        // Handle page size change
        const handlePageSizeChange = (newPageSize) => {
            emit('page-size-change', parseInt(newPageSize));
        };

        // Computed properties for pagination
        const showingFrom = computed(() => {
            return Math.min((props.currentPage - 1) * props.pageSize + 1, props.totalCount);
        });

        const showingTo = computed(() => {
            return Math.min(props.currentPage * props.pageSize, props.totalCount);
        });

        const canGoPrevious = computed(() => {
            return props.currentPage > 1;
        });

        const canGoNext = computed(() => {
            return props.currentPage * props.pageSize < props.totalCount;
        });

        return {
            openConversationDetail,
            goToPage,
            handlePageSizeChange,
            showingFrom,
            showingTo,
            canGoPrevious,
            canGoNext,
            // Icons
            ExclamationTriangleIcon,
            ChevronLeftIcon,
            ChevronRightIcon,
            EyeIcon
        };
    },
    template: `
        <BaseDashboardCard size="auto" :loading="isLoading" card-class="min-h-[500px]">
            <div class="conversation-list-container">
                <!-- Empty State -->
                <div v-if="!isLoading && conversations.length === 0" class="flex flex-col items-center justify-center py-12">
                    <div class="text-4xl mb-4">🔍</div>
                    <p class="text-base-content/70 text-sm">没有找到符合条件的会话</p>
                </div>

                <!-- Conversations Table -->
                <div v-else-if="!isLoading" class="overflow-x-auto">
                    <table class="table rounded-xl overflow-hidden">
                        <thead>
                            <tr>
                                <th>用户</th>
                                <th>标题</th>
                                <th class="hidden md:table-cell">时间</th>
                                <th class="hidden md:table-cell w-24 text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="conversation in conversations" :key="conversation.id"
                                @click="openConversationDetail(conversation)"
                                class="hover:bg-base-200/50 cursor-pointer">
                                <td>
                                    <div class="flex flex-col">
                                        <div class="font-medium">{{ conversation.username }}</div>
                                        <div class="text-xs opacity-70">{{ conversation.email || '<EMAIL>' }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="flex items-center gap-2">
                                        <span class="truncate max-w-xs">{{ conversation.title }}</span>
                                        <span v-if="conversation.isBadCase" class="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium" :class="{'bg-red-100 text-red-800': true, 'dark-mode-badge': true}">
                                            Bad Case
                                        </span>
                                    </div>
                                </td>
                                <td class="hidden md:table-cell text-base-content/70">{{ conversation.lastMessageTime }}</td>
                                <td class="hidden md:table-cell text-center">
                                    <button class="btn btn-ghost btn-xs" @click.stop="openConversationDetail(conversation)">
                                        查看
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 py-4">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                            <div class="text-sm text-base-content/70">
                                显示 {{ showingFrom }} - {{ showingTo }} 条，共 {{ totalCount }} 条
                            </div>
                            <!-- 分页大小选择器 -->
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-base-content/70">每页显示:</span>
                                <select
                                    class="select select-sm select-bordered w-24 h-8 min-h-8 text-sm dark:border-base-content/20 dark:border"
                                    :value="pageSize"
                                    @change="handlePageSizeChange($event.target.value)"
                                >
                                    <option
                                        v-for="option in pageSizeOptions"
                                        :key="option.value"
                                        :value="option.value"
                                    >
                                        {{ option.value }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="join">
                            <button
                                class="join-item btn btn-sm"
                                :disabled="!canGoPrevious"
                                @click="goToPage(currentPage - 1)"
                            >
                                <span v-html="ChevronLeftIcon"></span>
                            </button>
                            <button class="join-item btn btn-sm btn-active">
                                {{ currentPage }}
                            </button>
                            <button
                                class="join-item btn btn-sm"
                                :disabled="!canGoNext"
                                @click="goToPage(currentPage + 1)"
                            >
                                <span v-html="ChevronRightIcon"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </BaseDashboardCard>
    `
};
