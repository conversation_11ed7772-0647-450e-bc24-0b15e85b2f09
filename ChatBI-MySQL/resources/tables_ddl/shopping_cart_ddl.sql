CREATE TABLE `shopping_cart` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primary key',
  `m_id` bigint(20) DEFAULT NULL COMMENT '门店ID, 关联查询`merchant`.`m_id`',
  `account_id` bigint(20) DEFAULT NULL COMMENT '子账号id，管理查询 `merchant_sub_account`.`account_id`',
  `biz_id` bigint(20) DEFAULT NULL COMMENT '业务id',
  `sku` varchar(50) DEFAULT NULL COMMENT 'sku, 关联查询`inventory`.`sku`',
  `parent_sku` varchar(50) DEFAULT NULL COMMENT '搭配购上级sku, 关联查询`inventory`.`sku`',
  `product_type` tinyint(4) DEFAULT NULL COMMENT '商品类型。0:普通商品，1:赠品，2:换购。',
  `quantity` int(11) DEFAULT NULL COMMENT '加购的商品数量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_index` (`m_id`,`account_id`,`sku`,`product_type`,`parent_sku`) COMMENT '唯一索引',
  KEY `biz_id_and_sku_index` (`biz_id`,`sku`) COMMENT 'bizId、sku普通索引'
) ENGINE=InnoDB AUTO_INCREMENT=******** DEFAULT CHARSET=utf8 COMMENT='用户的购物车列表，一个门店(m_id)可能有多个子账户(account_id),每个子账户都有自己的购物车列表';