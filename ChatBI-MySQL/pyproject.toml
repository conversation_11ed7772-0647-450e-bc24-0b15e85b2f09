# pyproject.toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "chatbi-mysql"
version = "0.1.0"
description = "…"
readme = "README.md"
requires-python = ">=3.9.6"
dependencies = [
    "flask>=2.0",
    "lark-oapi>=1.4.14",
    "mysql-connector-python>=8.0",
    "openai>=1.0",
    "openai-agents>=0.0.16",
    "pandas>=2.2.3",
    "python-dotenv>=0.19",
    "pyyaml>=6.0",
    "aiohttp>=3.11.18"
]

[tool.setuptools.packages.find]
# 指定在 src 下找包
where = ["src"]
include = ["parent*"]

[[tool.uv.index]]
name = "tsinghua"
url = "http://mirrors.aliyun.com/pypi/simple/"
default = true