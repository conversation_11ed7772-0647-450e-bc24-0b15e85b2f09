"""
Permission handling for agent bots.
"""

import os
from src.services.xianmudb.query_service import execute_business_query
from typing import Dict, Any

from src.utils.logger import logger
from src.utils.in_memory_cache import in_memory_cache # 导入缓存装饰器

# Load system admin job titles from environment
system_admin_job_titles_str = os.getenv(
    "SYSTEM_ADMIN_JOB_TITLES", "创始人,技术负责人,后端,销售策划,前端,测试,数据开发,产品经理,运营"
)
system_admin_job_titles = set(
    title.strip() for title in system_admin_job_titles_str.split(",") if title.strip()
)


@in_memory_cache(expire_seconds=1200) # 应用缓存装饰器，缓存1200秒
def get_user_area_no_permissions(admin_id: str) -> str:
    """
    获取用户的区域权限

    Args:
        user_name: 用户名

    Returns:
        str: 逗号分隔的区域编号列表，表示用户有权访问的区域
    """
    sql = f"""SELECT
  admin.`admin_id`,
  admin.`realname`,
  GROUP_CONCAT(`admin_data_permission`.permission_value) permission_value_list
FROM
  admin
  JOIN `admin_data_permission` ON admin.`admin_id` = `admin_data_permission`.`admin_id`
WHERE
  admin.admin_id = '{admin_id}' and admin_data_permission.type = 1
GROUP BY admin.`admin_id`,
  admin.`realname`;"""
    permission_area_no_list = execute_business_query(sql)
    if permission_area_no_list.success and permission_area_no_list.data and len(
        permission_area_no_list.data
    ) > 0:
        return permission_area_no_list.data[0][2]
    elif not permission_area_no_list.success:
        logger.error(
            f"Error fetching area permissions for user {admin_id}: {permission_area_no_list.error}"
        )
        return ""
    else:
        logger.warning(f"No area permissions found for user {admin_id}")
        return ""


def get_user_permission(user_info: Dict[str, Any]) -> str:
    """
    Get the permission description for a user.

    Args:
        user_info: Dictionary containing user information

    Returns:
        str: Description of the user's permissions
    """
    job_title = user_info.get("job_title","未知职位")
    user_name = user_info.get("name","未知用户")
    admin_id = user_info.get("admin_id")
    crm_bd_org_rank = "M1" if job_title == "销售主管" else "M2" if job_title == "销售经理" else ""
    user_mark = f"用户名:{user_name}, 职务:{job_title}-{crm_bd_org_rank}"

    if job_title in ["销售专员", "销售经理", "销售主管"]:
        user_mark = f"{user_mark}, bd_id(crm_bd_org.bd_id):{admin_id}"

    if job_title in system_admin_job_titles:
        return f"{user_mark},为系统管理员，无数据权限限制，可访问所有的数据"

    if "销售专员" == job_title:
        if admin_id:
            return f"{user_mark},只可访问归属他的门店(也就是他的私海内的门店,即:`follow_up_relation`.`admin_id`={admin_id})的门店之数据)"
        else:
            logger.warning(
                f"Admin ID not found for 销售专员 {user_name}. Cannot apply specific permission."
            )
            return f"{user_mark}, 无法确定其具体门店权限，因为缺少 admin_id。"

    if "销售" in job_title:
        area_no_list = get_user_area_no_permissions(admin_id=admin_id)
        if area_no_list:
            return f"{user_mark},只可访问他所负责的区域(即:`area`.`area_no` in ({area_no_list}))的门店之数据"
        else:
            logger.warning(f"No area permissions found for 销售 {user_name}")
            return f"{user_mark},无法确定其具体区域权限，因为在admin_data_permission表中未找到相关记录。"

    # Default case for other job titles
    return f"{user_mark},无特殊数据权限限制"
