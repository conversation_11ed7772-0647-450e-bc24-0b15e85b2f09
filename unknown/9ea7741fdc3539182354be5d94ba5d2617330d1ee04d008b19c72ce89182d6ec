/**
 * 抽屉布局样式
 *
 * 定义移动端抽屉式侧边栏的布局和行为
 * 不依赖DaisyUI，使用原生CSS实现抽屉功能
 * 保持与原有设计一致，但提高Safari兼容性
 */

/* 抽屉容器 */
.custom-drawer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* 抽屉内容区域 */
.custom-drawer-content {
    width: 100%;
    height: 100%;
    transition: transform var(--duration-normal) ease-out;
}

/* 抽屉侧边栏 */
.custom-drawer-side {
    position: fixed;
    top: 0;
    left: 0;
    width: 16rem; /* 256px = 64 * 4 (tailwind w-64) */
    height: 100%;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform var(--duration-normal) ease-out;
}

/* 抽屉打开时的侧边栏 */
.custom-drawer.drawer-open .custom-drawer-side {
    transform: translateX(0);
}

/* 抽屉遮罩层 */
.custom-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--duration-normal) ease-out,
                visibility var(--duration-normal) ease-out;
}

/* 抽屉打开时的遮罩层 */
.custom-drawer.drawer-open .custom-drawer-overlay {
    opacity: 1;
    visibility: visible;
}

/* 桌面视图下的抽屉 */
@media (min-width: 1024px) {
    .custom-drawer-mobile {
        display: none;
    }
}

/* 移动视图下的抽屉 */
@media (max-width: 1023px) {
    .custom-drawer-desktop {
/* 修复移动端抽屉遮罩层阻挡侧边栏点击问题 */
@media (max-width: 1023px) {
  .custom-drawer.drawer-open .custom-drawer-overlay {
    left: 16rem;
    width: calc(100% - 16rem);
  }
}
        display: none;
    }
}

/* 确保Safari兼容性的额外修复 */
.custom-drawer-side {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    -webkit-transition: -webkit-transform var(--duration-normal) ease-out;
    transition: -webkit-transform var(--duration-normal) ease-out;
    transition: transform var(--duration-normal) ease-out;
    transition: transform var(--duration-normal) ease-out, -webkit-transform var(--duration-normal) ease-out;
    will-change: transform;
}

.custom-drawer.drawer-open .custom-drawer-side {
    -webkit-transform: translateX(0);
    transform: translateX(0);
}

/* 修复Safari中的层叠上下文问题 */
.custom-drawer-side {
    transform: translate3d(-100%, 0, 0);
}

.custom-drawer.drawer-open .custom-drawer-side {
    transform: translate3d(0, 0, 0);
}

/* 覆盖 DaisyUI drawer 组件的过渡时间 */
.drawer-side > .drawer-overlay {
    transition-duration: var(--duration-normal) !important;
}

.drawer-side {
    transition-duration: var(--duration-normal) !important;
}
