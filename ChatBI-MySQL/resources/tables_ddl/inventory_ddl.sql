CREATE TABLE `inventory` (
  `inv_id` bigint(30) NOT NULL AUTO_INCREMENT COMMENT 'SKU ID，自增主键',
  `sku` varchar(30) NOT NULL COMMENT 'SKU编号，唯一标识商品，如：N001S01R005',
  `pd_id` bigint(30) DEFAULT NULL COMMENT '所属的商品ID，关联商品表:products.pd_id',
  `origin` varchar(100) DEFAULT NULL COMMENT '商品产地，如"中国"',
  `unit` varchar(5) DEFAULT NULL COMMENT 'SKU包装单位，如"箱"、"个"',
  `after_sale_unit` varchar(5) DEFAULT NULL COMMENT 'SKU的售后单位，如："袋"，"G"，"罐"',
  `maturity` varchar(36) DEFAULT NULL COMMENT 'SKU生熟度，如"生鲜"、"熟食"',
  `weight` varchar(100) DEFAULT NULL COMMENT 'SKU包装规格描述，如：1KG*10袋，900G*12罐',
  `outdated` int(11) DEFAULT '0' COMMENT 'SKU状态：-1上新中 0使用中 1已删除',
  `volume` varchar(255) DEFAULT NULL COMMENT 'SKU体积，格式"长*宽*高"，如"0.230*0.230*0.230"',
  `weight_num` decimal(10,2) DEFAULT NULL COMMENT 'SKU重量(kg)，如3.00', 
  `sub_type` tinyint(4) DEFAULT NULL COMMENT 'SKU性质，1:代销不入仓(俗称‘全品类’)、2:代销入仓(俗称‘全品类’)、3:自营(经销)(我司自行采购、入库、销售)、4:代仓-代仓(大客户自己的商品，我司仅代为操作出入库以及存储、配送)、5:顺鹿达商品(鲜果POP)',
  `add_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `audit_status` int(11) DEFAULT '1' COMMENT '审核状态：0待审核 1通过 2拒绝',
  `sku_name` varchar(60) DEFAULT NULL COMMENT 'SKU名称, 比如‘’。这个字段比较少使用，在涉及到商品名字的问题中，应该尽可能使用`products`.`pd_name`来匹配。',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `video_url` varchar(256) DEFAULT '' COMMENT '商品视频链接',
  PRIMARY KEY (`inv_id`),
  KEY `IN_sku` (`sku`,`outdated`),
  KEY `FK_product_pd_id` (`pd_id`,`outdated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品SKU信息表，记录商品SKU、名字、规格、SPU(pd_id)、是否自营(经销)/代仓/是否‘全品类’等核心信息。当用户提到‘全品类’时，指的就是sub_type in (1,2)的SKU。';